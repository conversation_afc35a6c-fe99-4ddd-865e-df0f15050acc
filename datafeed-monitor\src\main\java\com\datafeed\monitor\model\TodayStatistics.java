package com.datafeed.monitor.model;

/**
 * 今日统计数据
 */
public class TodayStatistics {
    
    /**
     * 发送数量
     */
    private Integer sendCount;
    
    /**
     * 失败数量
     */
    private Integer failureCount;
    
    /**
     * 成功数量
     */
    private Integer successCount;
    
    /**
     * 成功率
     */
    private Double successRate;
    
    public TodayStatistics() {}
    
    public TodayStatistics(Integer sendCount, Integer failureCount, Integer successCount, Double successRate) {
        this.sendCount = sendCount;
        this.failureCount = failureCount;
        this.successCount = successCount;
        this.successRate = successRate;
    }
    
    public Integer getSendCount() {
        return sendCount;
    }
    
    public void setSendCount(Integer sendCount) {
        this.sendCount = sendCount;
    }
    
    public Integer getFailureCount() {
        return failureCount;
    }
    
    public void setFailureCount(Integer failureCount) {
        this.failureCount = failureCount;
    }
    
    public Integer getSuccessCount() {
        return successCount;
    }
    
    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }
    
    public Double getSuccessRate() {
        return successRate;
    }
    
    public void setSuccessRate(Double successRate) {
        this.successRate = successRate;
    }
    
    @Override
    public String toString() {
        return "TodayStatistics{" +
                "sendCount=" + sendCount +
                ", failureCount=" + failureCount +
                ", successCount=" + successCount +
                ", successRate=" + successRate +
                '}';
    }
}
