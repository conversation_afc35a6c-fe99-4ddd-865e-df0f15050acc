<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataFeed Monitor API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .api-test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .api-url { font-weight: bold; color: #0066cc; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>DataFeed Monitor API 测试页面</h1>
    
    <div class="api-test">
        <div class="api-url">GET /api/datafeed/health</div>
        <button onclick="testAPI('/api/datafeed/health', 'health')">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="api-test">
        <div class="api-url">GET /api/datafeed/dashboard</div>
        <button onclick="testAPI('/api/datafeed/dashboard', 'dashboard')">测试仪表板数据</button>
        <div id="dashboard-result" class="result"></div>
    </div>

    <div class="api-test">
        <div class="api-url">GET /api/datafeed/users/online</div>
        <button onclick="testAPI('/api/datafeed/users/online', 'online')">测试在线用户</button>
        <div id="online-result" class="result"></div>
    </div>

    <div class="api-test">
        <div class="api-url">GET /api/datafeed/statistics/today</div>
        <button onclick="testAPI('/api/datafeed/statistics/today', 'today')">测试今日统计</button>
        <div id="today-result" class="result"></div>
    </div>

    <div class="api-test">
        <div class="api-url">GET /api/datafeed/trend/realtime</div>
        <button onclick="testAPI('/api/datafeed/trend/realtime?timeUnit=minute&minutes=60', 'trend')">测试实时趋势</button>
        <div id="trend-result" class="result"></div>
    </div>

    <div class="api-test">
        <div class="api-url">GET /api/datafeed/products/statistics</div>
        <button onclick="testAPI('/api/datafeed/products/statistics', 'products')">测试产品统计</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="api-test">
        <button onclick="testAllAPIs()">测试所有API</button>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8082/datafeed-monitor';

        async function testAPI(endpoint, resultId) {
            const resultDiv = document.getElementById(resultId + '-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(BASE_URL + endpoint);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ 成功 (${response.status})</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ 失败 (${response.status})</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ 网络错误</strong><br>
                    ${error.message}
                `;
            }
        }

        async function testAllAPIs() {
            await testAPI('/api/datafeed/health', 'health');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPI('/api/datafeed/dashboard', 'dashboard');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPI('/api/datafeed/users/online', 'online');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPI('/api/datafeed/statistics/today', 'today');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPI('/api/datafeed/trend/realtime?timeUnit=minute&minutes=60', 'trend');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPI('/api/datafeed/products/statistics', 'products');
        }
    </script>
</body>
</html>
