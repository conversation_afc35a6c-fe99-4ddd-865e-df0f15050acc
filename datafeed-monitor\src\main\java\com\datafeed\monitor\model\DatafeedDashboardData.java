package com.datafeed.monitor.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * Datafeed仪表板数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DatafeedDashboardData {
    
    /**
     * 当前在线用户数
     */
    private Integer onlineUserCount;
    
    /**
     * 在线用户列表
     */
    private List<String> onlineUsers;
    
    /**
     * 当日发送总数
     */
    private Long todaySendCount;
    
    /**
     * 发送失败数量
     */
    private Long failureCount;
    
    /**
     * 成功率
     */
    private Double successRate;
    
    /**
     * 数据发送趋势
     */
    private List<TrendDataPoint> trendData;
    
    /**
     * 各产品发送数量统计
     */
    private List<ProductStatistics> productStatistics;
    
    /**
     * 趋势数据点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TrendDataPoint {
        /**
         * 时间点
         */
        private String timePoint;
        
        /**
         * 发送数量
         */
        private Long count;
    }
    
    /**
     * 产品统计数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProductStatistics {
        /**
         * 产品代码
         */
        private String productCode;
        
        /**
         * 发送数量
         */
        private Long count;
        
        /**
         * 占比
         */
        private Double percentage;
    }
}
