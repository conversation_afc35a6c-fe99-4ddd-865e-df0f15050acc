package com.innodealing.skywalking.sdk.service;

import com.innodealing.skywalking.sdk.module.ErrorLogDTO;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;

/**
 * SkyWalking错误日志处理器接口
 */
public interface SkywalkingErrorLogHandler {
    
    /**
     * 获取错误日志
     *
     * @param serverName         服务名称
     * @param serverId          服务ID
     * @param endpointNameFilter 端点名称过滤器
     * @param start             开始时间
     * @param end               结束时间
     * @return 错误日志DTO
     * @throws Exception 异常
     */
    Optional<ErrorLogDTO> getErrorLog(String serverName, int serverId, Set<String> endpointNameFilter, 
                                     LocalDateTime start, LocalDateTime end) throws Exception;
}
