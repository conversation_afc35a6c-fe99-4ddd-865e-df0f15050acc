package com.datafeed.monitor.repository;

import com.datafeed.monitor.entity.DatafeedOriginMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * datafeed原始消息Repository
 */
@Repository
public interface DatafeedOriginMessageRepository extends JpaRepository<DatafeedOriginMessage, Long> {
    
    /**
     * 统计指定时间范围内的消息数量
     */
    @Query("SELECT COUNT(m) FROM DatafeedOriginMessage m WHERE m.createTime >= :startTime AND m.createTime < :endTime")
    Long countByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按时间格式分组统计消息数量（按分钟）
     */
    @Query(value = "SELECT COUNT(*) as count, DATE_FORMAT(create_time, '%Y-%m-%d %H:%i') as timeGroup " +
           "FROM datafeed_origin_message " +
           "WHERE create_time >= :startTime AND create_time < :endTime " +
           "GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d %H:%i') " +
           "ORDER BY timeGroup", nativeQuery = true)
    List<Object[]> countByMinuteGrouping(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按时间格式分组统计消息数量（按秒）
     */
    @Query(value = "SELECT COUNT(*) as count, DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as timeGroup " +
           "FROM datafeed_origin_message " +
           "WHERE create_time >= :startTime AND create_time < :endTime " +
           "GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') " +
           "ORDER BY timeGroup", nativeQuery = true)
    List<Object[]> countBySecondGrouping(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按产品代码分组统计消息数量
     */
    @Query("SELECT m.productCode, COUNT(m) FROM DatafeedOriginMessage m " +
           "WHERE m.createTime >= :startTime AND m.createTime < :endTime " +
           "GROUP BY m.productCode")
    List<Object[]> countByProductCodeGrouping(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找指定时间范围内的消息
     */
    List<DatafeedOriginMessage> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找指定产品代码和时间范围内的消息
     */
    List<DatafeedOriginMessage> findByProductCodeAndCreateTimeBetween(String productCode, LocalDateTime startTime, LocalDateTime endTime);
}
