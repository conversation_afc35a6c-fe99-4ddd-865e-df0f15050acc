package com.datafeed.monitor.service.impl;

import com.datafeed.monitor.model.DatafeedStatistics;
import com.datafeed.monitor.model.StatisticsRequest;
import com.datafeed.monitor.model.StatisticsResponse;
import com.datafeed.monitor.service.DatafeedStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 简单的数据发送统计服务实现（内存存储）
 */
@Slf4j
@Service
@ConditionalOnMissingBean(name = "datafeedStatisticsServiceImpl")
public class SimpleDatafeedStatisticsServiceImpl implements DatafeedStatisticsService {
    
    // 使用内存存储统计数据
    private final Map<String, DatafeedStatistics> statisticsStore = new ConcurrentHashMap<>();
    private final Set<String> dataSources = ConcurrentHashMap.newKeySet();
    private final Set<String> targetSystems = ConcurrentHashMap.newKeySet();
    
    @Override
    public void recordStatistics(DatafeedStatistics statistics) {
        try {
            // 生成key
            String key = generateKey(statistics);
            
            // 存储统计数据
            statisticsStore.put(key, statistics);
            
            // 更新数据源和目标系统列表
            if (statistics.getDataSource() != null) {
                dataSources.add(statistics.getDataSource());
            }
            if (statistics.getTargetSystem() != null) {
                targetSystems.add(statistics.getTargetSystem());
            }
            
            log.debug("记录统计数据: {}", statistics);
        } catch (Exception e) {
            log.error("记录统计数据失败", e);
        }
    }
    
    @Override
    public void batchRecordStatistics(List<DatafeedStatistics> statisticsList) {
        for (DatafeedStatistics statistics : statisticsList) {
            recordStatistics(statistics);
        }
    }
    
    @Override
    public StatisticsResponse getStatistics(StatisticsRequest request) {
        try {
            // 过滤符合条件的统计数据
            List<DatafeedStatistics> filteredStats = statisticsStore.values().stream()
                    .filter(stats -> matchesFilter(stats, request))
                    .filter(stats -> isInTimeRange(stats, request))
                    .collect(Collectors.toList());
            
            // 计算统计结果
            return calculateStatistics(filteredStats, request.getTimeUnit());
            
        } catch (Exception e) {
            log.error("查询统计数据失败", e);
            return StatisticsResponse.builder()
                    .timeUnit(request.getTimeUnit())
                    .dataPoints(new ArrayList<>())
                    .build();
        }
    }
    
    @Override
    public StatisticsResponse getRealtimeStatistics(String timeUnit, int minutes) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMinutes(minutes);
        
        StatisticsRequest request = StatisticsRequest.builder()
                .timeUnit(timeUnit)
                .startTime(startTime)
                .endTime(endTime)
                .build();
        
        return getStatistics(request);
    }
    
    @Override
    public int cleanupExpiredData(int retentionDays) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
        
        int removedCount = 0;
        Iterator<Map.Entry<String, DatafeedStatistics>> iterator = statisticsStore.entrySet().iterator();
        
        while (iterator.hasNext()) {
            Map.Entry<String, DatafeedStatistics> entry = iterator.next();
            DatafeedStatistics stats = entry.getValue();
            
            if (stats.getTimestamp().toLocalDateTime().isBefore(cutoffTime)) {
                iterator.remove();
                removedCount++;
            }
        }
        
        log.info("清理{}天前的过期数据，共清理{}条记录", retentionDays, removedCount);
        return removedCount;
    }
    
    @Override
    public List<String> getDataSources() {
        return new ArrayList<>(dataSources);
    }
    
    @Override
    public List<String> getTargetSystems() {
        return new ArrayList<>(targetSystems);
    }
    
    /**
     * 生成存储key
     */
    private String generateKey(DatafeedStatistics statistics) {
        return String.format("%s_%s_%d", 
                statistics.getTimeUnit(),
                statistics.getTimestamp().toString(),
                System.nanoTime());
    }
    
    /**
     * 检查统计数据是否匹配过滤条件
     */
    private boolean matchesFilter(DatafeedStatistics statistics, StatisticsRequest request) {
        if (request.getDataSource() != null && 
            !request.getDataSource().equals(statistics.getDataSource())) {
            return false;
        }
        
        if (request.getTargetSystem() != null && 
            !request.getTargetSystem().equals(statistics.getTargetSystem())) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查统计数据是否在时间范围内
     */
    private boolean isInTimeRange(DatafeedStatistics statistics, StatisticsRequest request) {
        LocalDateTime statsTime = statistics.getTimestamp().toLocalDateTime();
        return !statsTime.isBefore(request.getStartTime()) && 
               !statsTime.isAfter(request.getEndTime());
    }
    
    /**
     * 计算统计结果
     */
    private StatisticsResponse calculateStatistics(List<DatafeedStatistics> statisticsList, String timeUnit) {
        if (statisticsList.isEmpty()) {
            return StatisticsResponse.builder()
                    .timeUnit(timeUnit)
                    .dataPoints(new ArrayList<>())
                    .build();
        }
        
        // 计算总计数据
        long totalSendCount = statisticsList.stream().mapToLong(DatafeedStatistics::getSendCount).sum();
        long totalDataVolume = statisticsList.stream().mapToLong(DatafeedStatistics::getDataVolume).sum();
        long totalSuccessCount = statisticsList.stream().mapToLong(DatafeedStatistics::getSuccessCount).sum();
        long totalFailureCount = statisticsList.stream().mapToLong(DatafeedStatistics::getFailureCount).sum();
        
        // 计算平均值和峰值
        double avgSendRate = statisticsList.stream().mapToLong(DatafeedStatistics::getSendCount).average().orElse(0.0);
        long peakSendRate = statisticsList.stream().mapToLong(DatafeedStatistics::getSendCount).max().orElse(0L);
        double successRate = totalSendCount > 0 ? (double) totalSuccessCount / totalSendCount * 100 : 0.0;
        double avgResponseTime = statisticsList.stream().mapToDouble(DatafeedStatistics::getAvgResponseTime).average().orElse(0.0);
        
        // 构建数据点
        List<StatisticsResponse.DataPoint> dataPoints = statisticsList.stream()
                .map(this::convertToDataPoint)
                .collect(Collectors.toList());
        
        return StatisticsResponse.builder()
                .totalSendCount(totalSendCount)
                .totalDataVolume(totalDataVolume)
                .totalSuccessCount(totalSuccessCount)
                .totalFailureCount(totalFailureCount)
                .avgSendRate(avgSendRate)
                .peakSendRate(peakSendRate)
                .successRate(successRate)
                .avgResponseTime(avgResponseTime)
                .timeUnit(timeUnit)
                .dataPoints(dataPoints)
                .build();
    }
    
    /**
     * 转换为数据点
     */
    private StatisticsResponse.DataPoint convertToDataPoint(DatafeedStatistics statistics) {
        return StatisticsResponse.DataPoint.builder()
                .timestamp(statistics.getTimestamp().toString())
                .sendCount(statistics.getSendCount())
                .dataVolume(statistics.getDataVolume())
                .successCount(statistics.getSuccessCount())
                .failureCount(statistics.getFailureCount())
                .responseTime(statistics.getAvgResponseTime())
                .build();
    }
}
