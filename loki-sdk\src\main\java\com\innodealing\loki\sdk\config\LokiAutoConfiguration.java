package com.innodealing.loki.sdk.config;

import com.innodealing.loki.sdk.service.LokiService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Loki SDK自动配置
 */
@Configuration
@EnableConfigurationProperties(LokiProperties.class)
@ConditionalOnProperty(prefix = "loki", name = "enabled", havingValue = "true", matchIfMissing = true)
public class LokiAutoConfiguration {

    /**
     * Loki服务
     */
    @Bean
    @ConditionalOnMissingBean
    public LokiService lokiService(LokiProperties properties) {
        return new LokiService(properties);
    }
}
