package com.innodealing.skywalking.sdk.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.*;

/**
 * SkyWalking SDK配置属性
 */
@ConfigurationProperties(prefix = "skywalking")
public class SkywalkingProperties {

    /**
     * SkyWalking服务器URL
     */
    private String url;

    /**
     * 是否启用SkyWalking监控
     */
    private boolean enabled = true;

    /**
     * SkyWalking服务配置列表
     */
    @NestedConfigurationProperty
    private Set<SkywalkingService> services = new LinkedHashSet<>();

    /**
     * 消息发送配置
     */
    @NestedConfigurationProperty
    private MessageConfig message = new MessageConfig();

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Set<SkywalkingService> getServices() {
        return Objects.isNull(services) ? new LinkedHashSet<>() : new LinkedHashSet<>(services);
    }

    public void setServices(Set<SkywalkingService> services) {
        this.services = Objects.isNull(services) ? new LinkedHashSet<>() : new LinkedHashSet<>(services);
    }

    public MessageConfig getMessage() {
        return message;
    }

    public void setMessage(MessageConfig message) {
        this.message = message;
    }

    /**
     * SkyWalking服务配置
     */
    public static class SkywalkingService {
        /**
         * 服务ID
         */
        private Integer id;

        /**
         * 服务名称
         */
        private String serverName;

        /**
         * 通知手机号列表，多个以逗号分隔
         */
        private String phones;

        /**
         * 忽略的URL列表
         */
        private Set<String> ignoreUrls = new HashSet<>();

        /**
         * 绑定的机器人ID列表，多个以逗号分隔
         */
        private String bindRobotIds;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getServerName() {
            return serverName;
        }

        public void setServerName(String serverName) {
            this.serverName = serverName;
        }

        public String getPhones() {
            return phones;
        }

        public void setPhones(String phones) {
            this.phones = phones;
        }

        public Set<String> getIgnoreUrls() {
            return Objects.isNull(ignoreUrls) ? new HashSet<>() : new HashSet<>(ignoreUrls);
        }

        public void setIgnoreUrls(Set<String> ignoreUrls) {
            this.ignoreUrls = Objects.isNull(ignoreUrls) ? new HashSet<>() : new HashSet<>(ignoreUrls);
        }

        public String getBindRobotIds() {
            return bindRobotIds;
        }

        public void setBindRobotIds(String bindRobotIds) {
            this.bindRobotIds = bindRobotIds;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SkywalkingService that = (SkywalkingService) o;
            return Objects.equals(id, that.id);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id);
        }
    }

    /**
     * 消息配置
     */
    public static class MessageConfig {
        /**
         * 是否发送消息
         */
        private boolean send = true;

        /**
         * 机器人配置列表
         */
        private List<Robot> robots = new ArrayList<>();

        public boolean isSend() {
            return send;
        }

        public void setSend(boolean send) {
            this.send = send;
        }

        public List<Robot> getRobots() {
            return Objects.isNull(robots) ? new ArrayList<>() : new ArrayList<>(robots);
        }

        public void setRobots(List<Robot> robots) {
            this.robots = Objects.isNull(robots) ? new ArrayList<>() : new ArrayList<>(robots);
        }
    }

    /**
     * 机器人配置
     */
    public static class Robot {
        private String id;
        private String desc;
        private String accessToken;
        private String secret;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }
    }
}
