package com.innodealing.skywalking.sdk.service.log;

import com.innodealing.skywalking.sdk.module.ErrorLogDTO;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorLogResponse;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 抽象钉钉日志处理器
 */
public abstract class AbstractDingdingLogHandler {

    protected final SkywalkingErrorLogResponse skywalkingErrorLogResponse;

    public AbstractDingdingLogHandler(SkywalkingErrorLogResponse skywalkingErrorLogResponse) {
        this.skywalkingErrorLogResponse = skywalkingErrorLogResponse;
    }

    public abstract Optional<ErrorLogDTO> handlerLog(LocalDateTime start, LocalDateTime end);
}
