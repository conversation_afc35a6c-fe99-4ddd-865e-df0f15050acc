import React from 'react';
import { Config<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';

const SimpleApp: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ padding: '24px', minHeight: '100vh', background: '#f0f2f5' }}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Alert
              message="DataFeed 监控平台"
              description="前端应用正在正常运行！"
              type="success"
              showIcon
              style={{ marginBottom: '24px' }}
            />
          </Col>
          
          <Col xs={24} sm={12} md={8}>
            <Card title="系统状态" hoverable>
              <p>✅ React 18 正常运行</p>
              <p>✅ Ant Design 组件正常</p>
              <p>✅ TypeScript 编译正常</p>
              <p>✅ Mock 数据服务就绪</p>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={8}>
            <Card title="功能测试" hoverable>
              <Button 
                type="primary" 
                block 
                style={{ marginBottom: '8px' }}
                onClick={() => alert('按钮点击正常！')}
              >
                点击测试
              </Button>
              <Button 
                block
                onClick={() => window.location.href = '/test'}
              >
                访问测试页面
              </Button>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={8}>
            <Card title="当前时间" hoverable>
              <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
                {new Date().toLocaleString()}
              </p>
              <p style={{ color: '#666' }}>
                页面加载时间: {new Date().toLocaleTimeString()}
              </p>
            </Card>
          </Col>
          
          <Col span={24}>
            <Card title="快速导航">
              <Row gutter={[8, 8]}>
                <Col>
                  <Button onClick={() => window.location.href = '/'}>
                    仪表板
                  </Button>
                </Col>
                <Col>
                  <Button onClick={() => window.location.href = '/data-statistics'}>
                    数据统计
                  </Button>
                </Col>
                <Col>
                  <Button onClick={() => window.location.href = '/account-monitor'}>
                    账号监控
                  </Button>
                </Col>
                <Col>
                  <Button onClick={() => window.location.href = '/test'}>
                    测试页面
                  </Button>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>
    </ConfigProvider>
  );
};

export default SimpleApp;
