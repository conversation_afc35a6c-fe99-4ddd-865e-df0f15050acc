import React from 'react';
import { ConfigProvider } from 'antd';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import zhCN from 'antd/locale/zh_CN';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import AccountMonitor from './pages/AccountMonitor';
import DataStatistics from './pages/DataStatistics';
import PermissionManagement from './pages/PermissionManagement';
import ConfigQuery from './pages/ConfigQuery';
import AlertCenter from './pages/AlertCenter';
import './App.css';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/account-monitor" element={<AccountMonitor />} />
            <Route path="/data-statistics" element={<DataStatistics />} />
            <Route path="/permission-management" element={<PermissionManagement />} />
            <Route path="/config-query" element={<ConfigQuery />} />
            <Route path="/alert-center" element={<AlertCenter />} />
          </Routes>
        </Layout>
      </Router>
    </ConfigProvider>
  );
};

export default App; 