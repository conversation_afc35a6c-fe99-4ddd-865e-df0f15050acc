-- 获取当前所有用户
SELECT DISTINCT user_account
FROM datafeed_user_template_cfg;

-- 获取在线状态
/**
  从接口获取
  curl -X GET "http://localhost:9303/dm-quickfixj-server/internal/session/list" -H "accept: *//*"
 */

-- 当日发送数量
SELECT COUNT(*)
FROM datafeed_origin_message
WHERE create_time >= '2025-06-19 00:00"00'
  AND create_time < '2025-06-20 00:00:00';

-- 发送失败数据数量
-- loki查询 {app="onshore-datafeed-processing"} |= `error messageId:`

-- 数据发送趋势 按分钟:'%Y-%m-%d %H:%i' 按秒:'%Y-%m-%d %H:%i:%s'
EXPLAIN SELECT COUNT(*), DATE_FORMAT(create_time, '%Y-%m-%d %H:%i')
FROM datafeed_origin_message
WHERE create_time >= '2025-06-19 00:00:00'
  AND create_time < '2025-06-20 00:00:00'
GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d %H:%i');

-- 查询每个模板在当天发送的数量
SELECT COUNT(*),product_code FROM datafeed_origin_message
WHERE create_time >= '2025-06-19 00:00:00'
  AND create_time < '2025-06-20 00:00:00'
GROUP BY product_code;