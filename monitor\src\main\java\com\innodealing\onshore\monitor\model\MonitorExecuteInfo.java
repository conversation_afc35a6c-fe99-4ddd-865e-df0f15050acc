package com.innodealing.onshore.monitor.model;


import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MonitorExecuteInfo {

    private String jobId;
    private String name;
    private String desc;
    private String group;
    private Integer triggerCode;
    private Integer handleCode;
    private Timestamp triggerTime;
    private Timestamp handleTime;
    private MonitorReport monitorReport;
    private Integer maxRetryCount = 2;
    private List<RetryRecord> retryHistory = new ArrayList<>();

    // 兼容原有构造函数的构造方法
    public MonitorExecuteInfo(String jobId, String name, String desc, String group,
                              Integer triggerCode, Integer handleCode,
                              Timestamp triggerTime, Timestamp handleTime,
                              MonitorReport monitorReport) {
        this.jobId = jobId;
        this.name = name;
        this.desc = desc;
        this.group = group;
        this.triggerCode = triggerCode;
        this.handleCode = handleCode;
        this.triggerTime = triggerTime;
        this.handleTime = handleTime;
        this.monitorReport = monitorReport;
        this.maxRetryCount = 2;
        this.retryHistory = new ArrayList<>();
    }

    /**
     * 获取重试次数
     *
     * @return 重试次数
     */
    public Integer getRetryCount() {
        return retryHistory.size();
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetryRecord {

        private Integer retryNumber;
        private Timestamp retryTime;
        private String reason;
        private Integer resultCode;

        public RetryRecord(Integer retryNumber, Timestamp retryTime, String reason) {
            this.retryNumber = retryNumber;
            this.retryTime = retryTime;
            this.reason = reason;
        }
    }
}
