-- auto-generated definition
create table datafeed_template
(
    id                      bigint auto_increment
        primary key,
    product_code            varchar(50)                        null comment '产品code',
    default_template_status tinyint(3)                         null comment '是否默认模板 1:默认模板 0:非默认模板',
    quickfix_keys           varchar(2000)                      null comment 'quickfix域数组',
    create_time             datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '创建时间',
    update_time             datetime default CURRENT_TIMESTAMP not null comment '更新时间'
)
    comment 'datafeed模板';

-- auto-generated definition
create table datafeed_user_filter_schema
(
    id             bigint auto_increment
        primary key,
    user_account   varchar(50)                        null comment 'quickfixj用户账号',
    template_id    bigint                             null comment '模板id',
    schema_content text                               null comment '方案内容',
    update_time    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    create_time    datetime default CURRENT_TIMESTAMP not null
)
    comment 'datafeed用户筛选方案 现阶段仅支持broker_type筛选';

create index idx_template_id
    on datafeed_user_filter_schema (template_id);

create index idx_user_account
    on datafeed_user_filter_schema (user_account);

-- auto-generated definition
create table datafeed_user_template_cfg
(
    id           bigint auto_increment
        primary key,
    user_account varchar(50)                        null comment '用户账号',
    template_id  bigint                             null comment '模板id',
    create_time  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '创建时间',
    update_time  datetime default CURRENT_TIMESTAMP not null comment '更新时间'
)
    comment '用户模板配置';

-- auto-generated definition
create table datafeed_origin_message
(
    id              bigint auto_increment comment 'id'
        primary key,
    product_code    varchar(50)                        null comment '产品code',
    message_seq     varchar(200)                       null comment '消息序列号 可以理解为trace_id',
    send_time       datetime                           null comment '发送时间',
    queue_hash_key  varchar(200)                       null comment '消息hash key用来保证消息有序性',
    message_content text                               null comment '消息内容',
    update_time     datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    create_time     datetime default CURRENT_TIMESTAMP not null comment '更新时间'
)
    comment 'datafeed原始消息';

create index idx_create_time_product
    on datafeed_origin_message (create_time, product_code);

create index idx_message_seq
    on datafeed_origin_message (message_seq);

create index idx_send_time
    on datafeed_origin_message (send_time);