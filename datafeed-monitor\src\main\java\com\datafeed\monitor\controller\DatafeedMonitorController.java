package com.datafeed.monitor.controller;

import com.datafeed.monitor.model.DatafeedDashboardData;
import com.datafeed.monitor.model.DatafeedQueryRequest;
import com.datafeed.monitor.service.DatafeedMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Datafeed监控控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/datafeed")
@Validated
public class DatafeedMonitorController {
    
    @Autowired
    private DatafeedMonitorService datafeedMonitorService;
    
    @GetMapping("/dashboard")
    public ResponseEntity<DatafeedDashboardData> getDashboardData() {
        try {
            DatafeedDashboardData data = datafeedMonitorService.getDashboardData();
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("获取仪表板数据失败", e);
            return ResponseEntity.ok(DatafeedDashboardData.builder().build());
        }
    }
    
    @GetMapping("/dashboard/range")
    public ResponseEntity<DatafeedDashboardData> getDashboardDataByRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            DatafeedDashboardData data = datafeedMonitorService.getDashboardData(startTime, endTime);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("获取指定时间范围仪表板数据失败", e);
            return ResponseEntity.ok(DatafeedDashboardData.builder().build());
        }
    }
    
    @PostMapping("/trend")
    public ResponseEntity<List<DatafeedDashboardData.TrendDataPoint>> getSendTrend(
            @RequestBody @Valid DatafeedQueryRequest request) {
        try {
            List<DatafeedDashboardData.TrendDataPoint> trendData = datafeedMonitorService.getSendTrend(request);
            return ResponseEntity.ok(trendData);
        } catch (Exception e) {
            log.error("获取发送趋势数据失败", e);
            return ResponseEntity.ok(List.of());
        }
    }
    
    @GetMapping("/trend/realtime")
    public ResponseEntity<List<DatafeedDashboardData.TrendDataPoint>> getRealtimeTrend(
            @RequestParam(defaultValue = "minute") 
            @Pattern(regexp = "^(minute|second)$", message = "时间单位只能是 minute 或 second")
            String timeUnit,
            @RequestParam(defaultValue = "60") int minutes) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusMinutes(minutes);
            
            DatafeedQueryRequest request = DatafeedQueryRequest.builder()
                    .timeUnit(timeUnit)
                    .startTime(startTime)
                    .endTime(endTime)
                    .build();
            
            List<DatafeedDashboardData.TrendDataPoint> trendData = datafeedMonitorService.getSendTrend(request);
            return ResponseEntity.ok(trendData);
        } catch (Exception e) {
            log.error("获取实时趋势数据失败", e);
            return ResponseEntity.ok(List.of());
        }
    }
    
    @GetMapping("/products/statistics")
    public ResponseEntity<List<DatafeedDashboardData.ProductStatistics>> getProductStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null || endTime == null) {
                // 默认查询当天数据
                LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
                startTime = today;
                endTime = today.plusDays(1);
            }
            
            List<DatafeedDashboardData.ProductStatistics> statistics = 
                    datafeedMonitorService.getProductStatistics(startTime, endTime);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取产品统计数据失败", e);
            return ResponseEntity.ok(List.of());
        }
    }
    
    @GetMapping("/users/online")
    public ResponseEntity<Map<String, Object>> getOnlineUsers() {
        try {
            List<String> onlineUsers = datafeedMonitorService.getOnlineUsers();
            Map<String, Object> response = new HashMap<>();
            response.put("count", onlineUsers.size());
            response.put("users", onlineUsers);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取在线用户失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("count", 0);
            response.put("users", List.of());
            return ResponseEntity.ok(response);
        }
    }
    
    @GetMapping("/users/all")
    public ResponseEntity<List<String>> getAllUsers() {
        try {
            List<String> allUsers = datafeedMonitorService.getAllUsers();
            return ResponseEntity.ok(allUsers);
        } catch (Exception e) {
            log.error("获取所有用户失败", e);
            return ResponseEntity.ok(List.of());
        }
    }
    
    @GetMapping("/statistics/today")
    public ResponseEntity<Map<String, Object>> getTodayStatistics() {
        try {
            Long todaySendCount = datafeedMonitorService.getTodaySendCount();
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime tomorrow = today.plusDays(1);
            Long failureCount = datafeedMonitorService.getFailureCount(today, tomorrow);
            
            Double successRate = todaySendCount > 0 ? 
                ((double)(todaySendCount - failureCount) / todaySendCount) * 100 : 0.0;
            
            Map<String, Object> response = new HashMap<>();
            response.put("sendCount", todaySendCount);
            response.put("failureCount", failureCount);
            response.put("successCount", todaySendCount - failureCount);
            response.put("successRate", successRate);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取今日统计数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("sendCount", 0L);
            response.put("failureCount", 0L);
            response.put("successCount", 0L);
            response.put("successRate", 0.0);
            return ResponseEntity.ok(response);
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "datafeed-monitor");
        response.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(response);
    }
}
