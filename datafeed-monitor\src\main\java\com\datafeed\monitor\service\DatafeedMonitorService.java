package com.datafeed.monitor.service;

import com.datafeed.monitor.model.DatafeedDashboardData;
import com.datafeed.monitor.model.DatafeedQueryRequest;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Datafeed监控服务接口
 */
public interface DatafeedMonitorService {
    
    /**
     * 获取仪表板数据
     * 
     * @return 仪表板数据
     */
    DatafeedDashboardData getDashboardData();
    
    /**
     * 获取指定时间范围的仪表板数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 仪表板数据
     */
    DatafeedDashboardData getDashboardData(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取数据发送趋势
     * 
     * @param request 查询请求
     * @return 趋势数据
     */
    List<DatafeedDashboardData.TrendDataPoint> getSendTrend(DatafeedQueryRequest request);
    
    /**
     * 获取产品统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 产品统计数据
     */
    List<DatafeedDashboardData.ProductStatistics> getProductStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取在线用户列表
     * 
     * @return 在线用户列表
     */
    List<String> getOnlineUsers();
    
    /**
     * 获取所有用户列表
     * 
     * @return 所有用户列表
     */
    List<String> getAllUsers();
    
    /**
     * 获取当日发送数量
     * 
     * @return 当日发送数量
     */
    Long getTodaySendCount();
    
    /**
     * 获取发送失败数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 失败数量
     */
    Long getFailureCount(LocalDateTime startTime, LocalDateTime endTime);
}
