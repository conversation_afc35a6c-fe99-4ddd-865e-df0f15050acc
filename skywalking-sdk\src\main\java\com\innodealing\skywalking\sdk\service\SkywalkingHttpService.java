package com.innodealing.skywalking.sdk.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorData;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorDataDetail;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * SkyWalking HTTP服务
 */
public class SkywalkingHttpService {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String skywalkingUrl;

    public SkywalkingHttpService(String skywalkingUrl) {
        this.skywalkingUrl = skywalkingUrl;
    }

    /**
     * 获取SkyWalking错误数据
     */
    public SkywalkingErrorData getSkywalkingErrorData(int serverId, LocalDateTime start, LocalDateTime end) throws Exception {
        String step = "MINUTE";
        String pattern = "yyyy-MM-dd HHmm";
        Duration between = Duration.between(start, end);
        if (between.getSeconds() >= 60 * 60) {
            step = "HOUR";
            pattern = "yyyy-MM-dd HH";
        }
        String body1 = "{\"query\":\"query queryTraces($condition: TraceQueryCondition) {\\n  traces: queryBasicTraces(condition: $condition) {\\n" +
                "    data: traces {\\n      key: segmentId\\n      endpointNames\\n" +
                "      duration\\n      start\\n      isError\\n      traceIds\\n    }" +
                "\\n    total\\n  }}\",\"variables\":{\"condition\":{\"queryDuration\":" +
                "{\"start\":\"" + formatDateTime(start, pattern) + "\",\"end\":\"" + formatDateTime(end, pattern) + "\",\"step\":\"" + step + "\"}" +
                ",\"traceState\":\"ERROR\",\"paging\":{\"pageNum\":1,\"pageSize\":50,\"needTotal\":true}," +
                "\"queryOrder\":\"BY_DURATION\",\"serviceId\":\"" + serverId + "\"}}}";
        HttpResponse<String> stringHttpResponse = Unirest.post("" + skywalkingUrl + "/graphql")
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "zh,zh-CN;q=0.9")
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("Origin", "" + skywalkingUrl + "")
                .header("Proxy-Connection", "keep-alive")
                .header("Referer", "" + skywalkingUrl + "/trace")
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36")
                .body(body1)
                .asString();
        String body = stringHttpResponse.getBody();
        JsonNode jsonNode = objectMapper.readTree(body).get("data").get("traces");
        return objectMapper.convertValue(jsonNode, SkywalkingErrorData.class);
    }

    /**
     * 获取SkyWalking所有数据
     */
    public SkywalkingErrorData getSkywalkingAllData(int serverId, LocalDateTime start, LocalDateTime end) throws Exception {
        String step = "MINUTE";
        String pattern = "yyyy-MM-dd HHmm";
        Duration between = Duration.between(start, end);
        if (between.getSeconds() >= 60 * 60) {
            step = "HOUR";
            pattern = "yyyy-MM-dd HH";
        }
        String body1 = "{\"query\":\"query queryTraces($condition: TraceQueryCondition) {\\n  traces: queryBasicTraces(condition: $condition) {\\n" +
                "    data: traces {\\n      key: segmentId\\n      endpointNames\\n" +
                "      duration\\n      start\\n      isError\\n      traceIds\\n    }" +
                "\\n    total\\n  }}\",\"variables\":{\"condition\":{\"queryDuration\":" +
                "{\"start\":\"" + formatDateTime(start, pattern) + "\",\"end\":\"" + formatDateTime(end, pattern) + "\",\"step\":\"" + step + "\"}" +
                ",\"traceState\":\"SUCCESS\",\"paging\":{\"pageNum\":1,\"pageSize\":15,\"needTotal\":true}," +
                "\"queryOrder\":\"BY_DURATION\",\"serviceId\":\"" + serverId + "\",\"endpointName\": \"api/\"}}}";
        HttpResponse<String> stringHttpResponse = Unirest.post("" + skywalkingUrl + "/graphql")
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "zh,zh-CN;q=0.9")
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("Origin", "" + skywalkingUrl + "")
                .header("Proxy-Connection", "keep-alive")
                .header("Referer", "" + skywalkingUrl + "/trace")
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36")
                .body(body1)
                .asString();
        String body = stringHttpResponse.getBody();
        JsonNode jsonNode = objectMapper.readTree(body).get("data").get("traces");
        return objectMapper.convertValue(jsonNode, SkywalkingErrorData.class);
    }

    /**
     * 获取SkyWalking错误数据详情
     */
    public SkywalkingErrorDataDetail getSkywalkingErrorDataDetail(String traceId) throws Exception {
        HttpResponse<String> response = Unirest.post(skywalkingUrl + "/graphql")
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "zh,zh-CN;q=0.9")
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("Origin", skywalkingUrl)
                .header("Proxy-Connection", "keep-alive")
                .header("Referer", skywalkingUrl + "/trace")
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36")
                .body("{\n    \"query\": \"query queryTrace($traceId: ID!) " +
                        "{\\n  trace: queryTrace(traceId: $traceId) {\\n    spans {\\n      traceId\\n   " +
                        "   segmentId\\n      spanId\\n      parentSpanId\\n      refs {\\n       " +
                        " traceId\\n        parentSegmentId\\n        parentSpanId\\n        type\\n      }\\n  " +
                        "    serviceCode\\n      startTime\\n      endTime\\n      endpointName\\n  " +
                        "    type\\n      peer\\n      component\\n      isError\\n      layer\\n    " +
                        "  tags {\\n        key\\n        value\\n      }\\n      logs {\\n       " +
                        " time\\n        data {\\n          key\\n          value\\n        }\\n  " +
                        "    }\\n    }\\n  }\\n  }\",\n    \"variables\": {\n        " +
                        "\"traceId\": \"" + traceId + "\"\n    }\n}")
                .asString();
        JsonNode jsonNode = objectMapper.readTree(response.getBody()).get("data").get("trace");
        return objectMapper.convertValue(jsonNode, SkywalkingErrorDataDetail.class);
    }

    private static String formatDateTime(LocalDateTime time, String pattern) {
        return time.format(DateTimeFormatter.ofPattern(pattern));
    }
}
