package com.innodealing.onshore.monitor;

import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorClient;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.model.MonitorReport;
import com.innodealing.onshore.monitor.report.DefaultReport;
import com.innodealing.onshore.monitor.service.MonitorService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("local")
class MonitorRetryTests {

    @Resource
    private MonitorService monitorService;

    /**
     * 测试超时重试功能的模拟监控器
     */
    @MonitorClient(desc = "超时重试测试监控器", enable = true)
    public static class TimeoutRetryTestMonitor implements Monitor {
        private static final AtomicInteger callCount = new AtomicInteger(0);
        
        @Override
        public boolean monitor() {
            return reportMonitor().isAccess();
        }

        @Override
        public MonitorReport reportMonitor() {
            int count = callCount.incrementAndGet();
            System.out.println("TimeoutRetryTestMonitor called, count: " + count);
            
            // 模拟超时：抛出异常来模拟超时情况
            throw new RuntimeException("Simulated timeout exception for testing retry mechanism");
        }
        
        public static void resetCallCount() {
            callCount.set(0);
        }
        
        public static int getCallCount() {
            return callCount.get();
        }
    }

    /**
     * 测试成功重试的模拟监控器
     */
    @MonitorClient(desc = "成功重试测试监控器", enable = true)
    public static class SuccessfulRetryTestMonitor implements Monitor {
        private static final AtomicInteger callCount = new AtomicInteger(0);
        
        @Override
        public boolean monitor() {
            return reportMonitor().isAccess();
        }

        @Override
        public MonitorReport reportMonitor() {
            int count = callCount.incrementAndGet();
            System.out.println("SuccessfulRetryTestMonitor called, count: " + count);
            
            if (count <= 2) {
                // 前两次调用模拟超时
                throw new RuntimeException("Simulated timeout exception for retry test, attempt: " + count);
            }
            
            // 第三次调用成功
            return new MonitorReport(true, DefaultReport.success());
        }
        
        public static void resetCallCount() {
            callCount.set(0);
        }
        
        public static int getCallCount() {
            return callCount.get();
        }
    }

    @Test
    void testExceptionWithMaxRetries() {
        TimeoutRetryTestMonitor.resetCallCount();

        // 执行监控，应该会异常并重试2次，最终失败
        MonitorExecuteInfo result = monitorService.execute(TimeoutRetryTestMonitor.class);

        // 验证结果
        assertNotNull(result);
        assertEquals(HandleCode.EXCEPTION, result.getHandleCode().intValue());
        assertEquals(2, result.getRetryCount().intValue()); // 应该重试了2次
        assertEquals(2, result.getMaxRetryCount().intValue());
        assertFalse(result.getRetryHistory().isEmpty()); // 应该有重试历史记录
        assertEquals(2, result.getRetryHistory().size()); // 应该有2条重试记录

        // 验证重试历史记录
        for (int i = 0; i < result.getRetryHistory().size(); i++) {
            MonitorExecuteInfo.RetryRecord record = result.getRetryHistory().get(i);
            assertEquals(i + 1, record.getRetryNumber().intValue());
            assertEquals("Task timeout, attempting retry", record.getReason());
            assertNotNull(record.getRetryTime());
        }

        // 验证调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, TimeoutRetryTestMonitor.getCallCount());

        System.out.println("Test completed - Final retry count: " + result.getRetryCount());
        System.out.println("Test completed - Retry history size: " + result.getRetryHistory().size());
        System.out.println("Test completed - Total call count: " + TimeoutRetryTestMonitor.getCallCount());
    }

    @Test
    void testSuccessfulRetryAfterExceptions() {
        SuccessfulRetryTestMonitor.resetCallCount();

        // 执行监控，前两次异常，第三次成功
        MonitorExecuteInfo result = monitorService.execute(SuccessfulRetryTestMonitor.class);

        // 验证结果
        assertNotNull(result);
        assertEquals(HandleCode.SUCCESS, result.getHandleCode().intValue());
        assertEquals(2, result.getRetryCount().intValue()); // 应该重试了2次
        assertEquals(2, result.getMaxRetryCount().intValue());
        assertFalse(result.getRetryHistory().isEmpty()); // 应该有重试历史记录
        assertEquals(2, result.getRetryHistory().size()); // 应该有2条重试记录

        // 验证最终成功
        assertNotNull(result.getMonitorReport());
        assertTrue(result.getMonitorReport().isAccess());

        // 验证调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, SuccessfulRetryTestMonitor.getCallCount());

        System.out.println("Test completed - Final retry count: " + result.getRetryCount());
        System.out.println("Test completed - Final status: " + result.getHandleCode());
        System.out.println("Test completed - Monitor success: " + result.getMonitorReport().isAccess());
        System.out.println("Test completed - Total call count: " + SuccessfulRetryTestMonitor.getCallCount());
    }
}
