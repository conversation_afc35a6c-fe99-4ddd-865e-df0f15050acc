# Monitor监控服务重试功能说明

## 功能概述

Monitor监控服务现已支持任务超时和执行异常的自动重试机制，提高了监控系统的可靠性和稳定性。

## 重试机制特性

### 1. 重试触发条件
- **任务超时**：当监控任务执行超过120秒时触发重试
- **执行异常**：当监控任务执行过程中抛出异常时触发重试

### 2. 重试配置
- **默认重试次数**：2次
- **重试间隔**：30秒
- **可配置参数**：
  ```yaml
  monitor:
    retry:
      maxCount: 2        # 最大重试次数
      delaySeconds: 30   # 重试间隔（秒）
  ```

### 3. 重试状态码
新增了 `HandleCode.RETRYING = 100` 状态码，用于标识正在重试的任务。

完整状态码列表：
- `RUNNING = 0`：运行中
- `RETRYING = 100`：重试中
- `SUCCESS = 200`：成功
- `FAIL = 500`：失败
- `EXCEPTION = 502`：异常

## 重试逻辑流程

```
监控任务执行
    ↓
执行超时/异常？
    ↓ 是
重试次数 < 最大重试次数？
    ↓ 是
设置状态为RETRYING
    ↓
记录重试信息
    ↓
延迟30秒
    ↓
重新执行监控任务
    ↓
成功？
    ↓ 否
继续重试直到达到最大次数
    ↓
达到最大重试次数后触发告警
```

## 重试信息记录

每次重试都会记录详细信息：

### MonitorExecuteInfo 新增字段
- `retryCount`：当前重试次数
- `maxRetryCount`：最大重试次数
- `retryHistory`：重试历史记录列表

### RetryRecord 重试记录
- `retryNumber`：重试序号
- `retryTime`：重试时间
- `reason`：重试原因
- `resultCode`：重试结果状态码

## 告警机制优化

- **延迟告警**：只有在重试次数用完后才触发告警
- **避免重复告警**：重试过程中不会重复发送告警消息
- **详细信息**：告警消息包含重试历史信息

## 使用示例

### 1. 基本监控器实现
```java
@MonitorClient(desc = "示例监控器", alterConsumer = FeishuAlterConsumer.class)
public class ExampleMonitor implements Monitor {
    @Override
    public MonitorReport reportMonitor() {
        // 监控逻辑
        // 如果执行超时或抛出异常，系统会自动重试
        return new MonitorReport(true, DefaultReport.success());
    }
}
```

### 2. 查看重试信息
```java
MonitorExecuteInfo result = monitorService.execute(ExampleMonitor.class);
System.out.println("重试次数: " + result.getRetryCount());
System.out.println("重试历史: " + result.getRetryHistory().size());
```

## 演示监控器

项目中包含了一个演示监控器 `RetryDemoMonitor`，用于展示重试功能：

```java
// 前两次调用会失败，第三次成功
// 可以观察到完整的重试过程
MonitorExecuteInfo result = monitorService.execute(RetryDemoMonitor.class);
```

## 测试验证

运行 `MonitorRetryTests` 测试类来验证重试功能：

```bash
# 运行重试功能测试
mvn test -Dtest=MonitorRetryTests
```

测试包含：
1. `testExceptionWithMaxRetries`：测试达到最大重试次数的情况
2. `testSuccessfulRetryAfterExceptions`：测试重试成功的情况

## 配置建议

### 生产环境配置
```yaml
monitor:
  retry:
    maxCount: 2        # 建议2-3次
    delaySeconds: 30   # 建议30-60秒
```

### 开发环境配置
```yaml
monitor:
  retry:
    maxCount: 1        # 减少重试次数加快测试
    delaySeconds: 10   # 减少延迟时间
```

## 注意事项

1. **资源消耗**：重试会增加系统资源消耗，请合理配置重试次数
2. **延迟影响**：重试会延长任务总执行时间
3. **日志记录**：所有重试过程都会记录详细日志
4. **告警延迟**：启用重试后，告警会有延迟（重试时间 × 重试次数）

## 监控指标

可以通过以下方式监控重试情况：
- 查看日志中的重试记录
- 统计 `HandleCode.RETRYING` 状态的任务数量
- 分析重试历史数据识别问题模式

## 版本信息

- **功能版本**：v1.1.0
- **兼容性**：向后兼容，不影响现有监控器
- **依赖**：无额外依赖
