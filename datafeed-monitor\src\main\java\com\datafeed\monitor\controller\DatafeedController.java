package com.datafeed.monitor.controller;

import com.datafeed.monitor.model.*;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * DataFeed监控接口控制器
 */
@RestController
@RequestMapping("/api/datafeed")
@CrossOrigin(origins = "*")
public class DatafeedController {
    
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final Random random = new Random();
    
    /**
     * 获取仪表板数据
     */
    @GetMapping("/dashboard")
    public DashboardData getDashboardData() {
        DashboardData data = new DashboardData();
        
        // 生成在线用户
        List<String> onlineUsers = generateOnlineUsers();
        data.setOnlineUserCount(onlineUsers.size());
        data.setOnlineUsers(onlineUsers);
        
        // 生成统计数据
        int todaySendCount = 15000 + random.nextInt(35000); // 15000-50000
        int failureCount = 50 + random.nextInt(500); // 50-550
        double successRate = Math.round((1.0 - (double)failureCount / todaySendCount) * 100 * 100.0) / 100.0;
        
        data.setTodaySendCount(todaySendCount);
        data.setFailureCount(failureCount);
        data.setSuccessRate(Math.max(95.0, successRate));
        
        // 生成趋势数据
        data.setTrendData(generateTrendData(24));
        
        // 生成产品统计
        data.setProductStatistics(generateProductStatistics());
        
        return data;
    }
    
    /**
     * 获取指定时间范围的仪表板数据
     */
    @GetMapping("/dashboard/range")
    public DashboardData getDashboardDataByRange(@RequestParam String startTime, @RequestParam String endTime) {
        // 对于mock数据，忽略时间范围参数，返回相同的数据结构
        return getDashboardData();
    }
    
    /**
     * 获取发送趋势数据
     */
    @PostMapping("/trend")
    public List<TrendDataPoint> getSendTrend(@RequestBody QueryRequest request) {
        // 根据时间单位生成不同密度的数据
        int dataPoints = "second".equals(request.getTimeUnit()) ? 300 : 60; // 秒级300个点，分钟级60个点
        return generateTrendData(dataPoints);
    }
    
    /**
     * 获取实时趋势数据
     */
    @GetMapping("/trend/realtime")
    public List<TrendDataPoint> getRealtimeTrend(
            @RequestParam(defaultValue = "minute") String timeUnit,
            @RequestParam(defaultValue = "60") Integer minutes) {
        
        int dataPoints = "second".equals(timeUnit) ? Math.min(minutes * 60, 300) : Math.min(minutes, 60);
        return generateTrendData(dataPoints);
    }
    
    /**
     * 获取产品统计数据
     */
    @GetMapping("/products/statistics")
    public List<ProductStatistics> getProductStatistics(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        return generateProductStatistics();
    }
    
    /**
     * 获取在线用户
     */
    @GetMapping("/users/online")
    public OnlineUsersResponse getOnlineUsers() {
        List<String> users = generateOnlineUsers();
        return new OnlineUsersResponse(users.size(), users);
    }
    
    /**
     * 获取所有用户
     */
    @GetMapping("/users/all")
    public List<String> getAllUsers() {
        return Arrays.asList(
            "user001", "user002", "user003", "user004", "user005",
            "user006", "user007", "user008", "user009", "user010",
            "admin001", "admin002", "admin003", "test001", "test002",
            "demo001", "demo002", "system001", "monitor001", "api001"
        );
    }
    
    /**
     * 获取今日统计数据
     */
    @GetMapping("/statistics/today")
    public TodayStatistics getTodayStatistics() {
        int sendCount = 20000 + random.nextInt(40000); // 20000-60000
        int failureCount = 100 + random.nextInt(800); // 100-900
        int successCount = sendCount - failureCount;
        double successRate = Math.round((double)successCount / sendCount * 100 * 100.0) / 100.0;
        
        return new TodayStatistics(sendCount, failureCount, successCount, successRate);
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "OK");
        result.put("timestamp", LocalDateTime.now().format(formatter));
        result.put("service", "datafeed-monitor");
        result.put("version", "1.0.0");
        return result;
    }
    
    // ========== 私有方法：生成Mock数据 ==========
    
    /**
     * 生成在线用户列表
     */
    private List<String> generateOnlineUsers() {
        List<String> allUsers = Arrays.asList(
            "user001", "user002", "user003", "user004", "user005",
            "user006", "user007", "user008", "user009", "user010",
            "admin001", "admin002", "test001", "test002", "demo001"
        );
        
        int count = 3 + random.nextInt(8); // 3-10个用户
        Collections.shuffle(allUsers);
        return allUsers.subList(0, Math.min(count, allUsers.size()));
    }
    
    /**
     * 生成趋势数据
     */
    private List<TrendDataPoint> generateTrendData(int dataPoints) {
        List<TrendDataPoint> data = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = dataPoints - 1; i >= 0; i--) {
            LocalDateTime time = now.minusMinutes(i);
            int baseCount = 80 + random.nextInt(120); // 80-200基础值
            double wave = Math.sin(i * Math.PI / 20) * 30; // 波动
            int count = Math.max(20, (int)(baseCount + wave + (random.nextGaussian() * 15)));
            
            data.add(new TrendDataPoint(time.format(formatter), count));
        }
        
        return data;
    }
    
    /**
     * 生成产品统计数据
     */
    private List<ProductStatistics> generateProductStatistics() {
        String[] products = {"BOND", "EQUITY", "FOREX", "COMMODITY", "CRYPTO"};
        List<ProductStatistics> stats = new ArrayList<>();
        int totalCount = 0;
        int[] counts = new int[products.length];
        
        // 生成随机数量
        for (int i = 0; i < products.length; i++) {
            counts[i] = 1000 + random.nextInt(5000); // 1000-6000
            totalCount += counts[i];
        }
        
        // 计算百分比
        for (int i = 0; i < products.length; i++) {
            double percentage = Math.round((double)counts[i] / totalCount * 100 * 100.0) / 100.0;
            stats.add(new ProductStatistics(products[i], counts[i], percentage));
        }
        
        return stats;
    }
}
