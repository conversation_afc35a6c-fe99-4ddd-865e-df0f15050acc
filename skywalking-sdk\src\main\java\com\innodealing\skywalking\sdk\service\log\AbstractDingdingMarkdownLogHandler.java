package com.innodealing.skywalking.sdk.service.log;

import com.innodealing.skywalking.sdk.module.ErrorLogDTO;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorLogResponse;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 抽象钉钉Markdown日志处理器
 */
public class AbstractDingdingMarkdownLogHandler extends AbstractDingdingLogHandler {

    public AbstractDingdingMarkdownLogHandler(SkywalkingErrorLogResponse skywalkingErrorLogResponse) {
        super(skywalkingErrorLogResponse);
    }

    @Override
    public Optional<ErrorLogDTO> handlerLog(LocalDateTime start, LocalDateTime end) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        String s = toMarkdownString(start, end);
        if (Objects.isNull(s)) {
            return Optional.empty();
        } else {
            String title = String.format("%s [%s - %s]", skywalkingErrorLogResponse.getServerName(), start.format(formatter), end.format(formatter));
            ErrorLogDTO errorLogDTO = new ErrorLogDTO(title, s);
            return Optional.of(errorLogDTO);
        }
    }

    public String toMarkdownString(LocalDateTime start, LocalDateTime end) {
        int total = skywalkingErrorLogResponse.getTotal();
        int ignoreTotal = skywalkingErrorLogResponse.getIgnoreTotal();
        Set<SkywalkingErrorLogResponse.Detail> details = skywalkingErrorLogResponse.getDetails();
        String serverName = skywalkingErrorLogResponse.getServerName();
        if (total <= 0 || total == ignoreTotal) {
            return null;
        }
        int size = details.size();
        String per = "";
        if (size != total) {
            per = String.format("相同报错已聚合%d个", total - size);
        }
        StringBuilder sb = new StringBuilder();

        int details_size = details.size();
        int j = 0;
        for (SkywalkingErrorLogResponse.Detail detail : details) {
            String colorStyle = "purple";
            String emo = ":SLIGHT:";
            if (detail.getErrorTitle() != null && detail.getErrorTitle().contains("数据库")) {
                colorStyle = "red";
                emo = ":TOASTED:";
            }

            j++;
            sb.append(String.format("%d. <text_tag color='" + colorStyle + "'>EndpointName: %s</text_tag>\n<text_tag color='" + colorStyle + "'>TraceId: %s</text_tag> <text_tag color='" + colorStyle + "'>%s</text_tag> " + emo + "\n", j, detail.getEndpointName(), detail.getTraceId(), detail.getErrorTitle()));
            String errorMessage = detail.getErrorMessage() == null ? "" : detail.getErrorMessage();
            String[] split = errorMessage.split("\n");
            StringBuilder detail_msg_sb = new StringBuilder();
            for (int i = 0; i < split.length; i++) {
                String s = String.format("   <text_tag color='" + colorStyle + "'>%s</text_tag>    ", split[i]);
                if (i == split.length - 1) {
                    s = s + getLokiUrl("  查看更多日志...", detail.getTraceId(), serverName);
                    if (j != details_size) {
                        s = s + "  " + "\n---";
                    }
                }
                detail_msg_sb.append(s).append("\n");
            }
            sb.append(detail_msg_sb);
        }
        String ignoreMsg = "";
        if (ignoreTotal != 0) {
            ignoreMsg = String.format(" 其中已忽略%d个", ignoreTotal);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        String projectName = getUrl(skywalkingErrorLogResponse.getServerName(), String.format("http://git.innodealing.cn/backend/%s", skywalkingErrorLogResponse.getServerName()));
        String title = String.format("%s [%s - %s]", projectName, start.format(formatter), end.format(formatter));
        return String.format("** %s **\n** 异常数：%s个    %s  %s **\n%s", title, total, per, ignoreMsg, sb).trim();
    }

    private String getUrl(String linkName, String url) {
        return String.format("[%s](%s)", linkName, url);
    }

    private String getLokiUrl(String linkName, String traceId, String serverName) {
        try {
            String params = String.format("{\"datasource\":\"f6d3ff8e-de72-4ca6-8e14-978ccd5c28f6\",\"queries\":[{\"refId\":\"A\",\"editorMode\":\"builder\",\"expr\":\"{app=\\\"%s\\\"} |= `%s`\",\"queryType\":\"range\"}]}",
                    serverName, traceId);
            String url = String.format("http://172.16.5.162:30300/explore?orgId=1&left=%s", URLEncoder.encode(params, "utf-8"));
            return "<link icon='chat_outlined' url='" + url + "' pc_url='' ios_url='' android_url=''>" + linkName + "</link>";
        } catch (UnsupportedEncodingException e) {
            return "";
        }
    }
}
