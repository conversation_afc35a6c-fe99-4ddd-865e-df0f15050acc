package com.datafeed.monitor.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 趋势数据点
 */
public class TrendDataPoint {
    
    /**
     * 时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String timePoint;
    
    /**
     * 数量
     */
    private Integer count;
    
    public TrendDataPoint() {}
    
    public TrendDataPoint(String timePoint, Integer count) {
        this.timePoint = timePoint;
        this.count = count;
    }
    
    public String getTimePoint() {
        return timePoint;
    }
    
    public void setTimePoint(String timePoint) {
        this.timePoint = timePoint;
    }
    
    public Integer getCount() {
        return count;
    }
    
    public void setCount(Integer count) {
        this.count = count;
    }
    
    @Override
    public String toString() {
        return "TrendDataPoint{" +
                "timePoint='" + timePoint + '\'' +
                ", count=" + count +
                '}';
    }
}
