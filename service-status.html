<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataFeed Monitor - 服务状态检查</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .service-card {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .service-card.online {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }
        .service-card.offline {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        }
        .service-card.checking {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        }
        .service-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .service-status {
            font-size: 1.2em;
            margin: 10px 0;
        }
        .service-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 1em;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .api-tests {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .api-test {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-checking { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 DataFeed Monitor 服务状态</h1>
        
        <div class="service-grid">
            <div id="backend-card" class="service-card checking">
                <div class="service-title">
                    <span class="status-indicator status-checking"></span>
                    后端服务
                </div>
                <div class="service-status">检查中...</div>
                <div class="service-url">http://localhost:8082</div>
                <button class="btn" onclick="checkBackend()">检查状态</button>
                <button class="btn" onclick="openBackendAPI()">打开API</button>
            </div>
            
            <div id="frontend-card" class="service-card checking">
                <div class="service-title">
                    <span class="status-indicator status-checking"></span>
                    前端应用
                </div>
                <div class="service-status">检查中...</div>
                <div class="service-url">http://localhost:3000</div>
                <button class="btn" onclick="checkFrontend()">检查状态</button>
                <button class="btn" onclick="openFrontend()">打开应用</button>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn success" onclick="checkAllServices()">🔄 检查所有服务</button>
            <button class="btn" onclick="openAllServices()">🌐 打开所有服务</button>
        </div>

        <div class="api-tests">
            <h3>📡 API 接口测试</h3>
            
            <div class="api-test">
                <strong>健康检查:</strong>
                <button class="btn" onclick="testAPI('/api/datafeed/health', 'health-result')">测试</button>
                <span id="health-result">未测试</span>
            </div>
            
            <div class="api-test">
                <strong>仪表板数据:</strong>
                <button class="btn" onclick="testAPI('/api/datafeed/dashboard', 'dashboard-result')">测试</button>
                <span id="dashboard-result">未测试</span>
            </div>
            
            <div class="api-test">
                <strong>在线用户:</strong>
                <button class="btn" onclick="testAPI('/api/datafeed/users/online', 'users-result')">测试</button>
                <span id="users-result">未测试</span>
            </div>
            
            <div class="api-test">
                <strong>实时趋势:</strong>
                <button class="btn" onclick="testAPI('/api/datafeed/trend/realtime?timeUnit=minute&minutes=60', 'trend-result')">测试</button>
                <span id="trend-result">未测试</span>
            </div>
        </div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:8082/datafeed-monitor';
        const FRONTEND_URL = 'http://localhost:3000';

        async function checkBackend() {
            const card = document.getElementById('backend-card');
            const indicator = card.querySelector('.status-indicator');
            const status = card.querySelector('.service-status');
            
            card.className = 'service-card checking';
            indicator.className = 'status-indicator status-checking';
            status.textContent = '检查中...';
            
            try {
                const response = await fetch(BACKEND_URL + '/api/datafeed/health');
                if (response.ok) {
                    card.className = 'service-card online';
                    indicator.className = 'status-indicator status-online';
                    status.textContent = '✅ 在线 - 服务正常';
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                card.className = 'service-card offline';
                indicator.className = 'status-indicator status-offline';
                status.textContent = '❌ 离线 - 无法连接';
            }
        }

        async function checkFrontend() {
            const card = document.getElementById('frontend-card');
            const indicator = card.querySelector('.status-indicator');
            const status = card.querySelector('.service-status');
            
            card.className = 'service-card checking';
            indicator.className = 'status-indicator status-checking';
            status.textContent = '检查中...';
            
            try {
                const response = await fetch(FRONTEND_URL);
                if (response.ok) {
                    card.className = 'service-card online';
                    indicator.className = 'status-indicator status-online';
                    status.textContent = '✅ 在线 - 应用可访问';
                } else {
                    throw new Error('应用响应异常');
                }
            } catch (error) {
                card.className = 'service-card offline';
                indicator.className = 'status-indicator status-offline';
                status.textContent = '❌ 离线 - 无法访问';
            }
        }

        async function checkAllServices() {
            await Promise.all([checkBackend(), checkFrontend()]);
        }

        function openBackendAPI() {
            window.open(BACKEND_URL + '/api/datafeed/health', '_blank');
        }

        function openFrontend() {
            window.open(FRONTEND_URL, '_blank');
        }

        function openAllServices() {
            openFrontend();
            setTimeout(() => openBackendAPI(), 500);
        }

        async function testAPI(endpoint, resultId) {
            const resultSpan = document.getElementById(resultId);
            resultSpan.textContent = '测试中...';
            resultSpan.style.color = '#ffc107';
            
            try {
                const response = await fetch(BACKEND_URL + endpoint);
                if (response.ok) {
                    resultSpan.textContent = '✅ 成功';
                    resultSpan.style.color = '#28a745';
                } else {
                    resultSpan.textContent = '❌ 失败 (' + response.status + ')';
                    resultSpan.style.color = '#dc3545';
                }
            } catch (error) {
                resultSpan.textContent = '❌ 错误';
                resultSpan.style.color = '#dc3545';
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            setTimeout(checkAllServices, 1000);
            // 每30秒自动检查一次
            setInterval(checkAllServices, 30000);
        };
    </script>
</body>
</html>
