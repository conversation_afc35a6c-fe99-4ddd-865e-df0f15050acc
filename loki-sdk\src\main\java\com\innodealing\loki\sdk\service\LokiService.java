package com.innodealing.loki.sdk.service;

import com.google.gson.Gson;
import com.innodealing.loki.sdk.config.LokiProperties;
import com.innodealing.loki.sdk.model.ErrorMsgDTO;
import com.innodealing.loki.sdk.model.LokiLog;
import com.innodealing.loki.sdk.model.LokiRangeQuery;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Loki服务类
 */
public class LokiService {
    
    private static final Logger logger = LoggerFactory.getLogger(LokiService.class);
    private static final Pattern TRACE_ID_PATTERN = Pattern.compile("\\[TID:(.*?)]");
    
    private final LokiProperties properties;
    private final OkHttpClient httpClient;
    private final Gson gson;

    public LokiService(LokiProperties properties) {
        this.properties = properties;
        this.gson = new Gson();
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(properties.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(properties.getReadTimeout(), TimeUnit.MILLISECONDS)
                .build();
    }

    /**
     * 查询范围日志数据
     *
     * @param lokiRangeQuery 范围查询条件
     * @return 日志信息
     */
    public LokiLog queryRange(LokiRangeQuery lokiRangeQuery) {
        String queryParams = lokiRangeQuery.toUrl();
        String url = String.format("%s/loki/api/v1/query_range?%s", properties.getUrl(), queryParams);
        
        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "*/*")
                .addHeader("Connection", "keep-alive")
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.body() != null) {
                String body = response.body().string();
                return gson.fromJson(body, LokiLog.class);
            }
        } catch (IOException e) {
            logger.error("查询Loki日志失败", e);
            throw new RuntimeException("查询Loki日志失败", e);
        }
        
        return new LokiLog();
    }

    /**
     * 查询指定服务的日志
     *
     * @param serverName 服务名称
     * @param start      开始时间
     * @param end        结束时间
     * @param msg        搜索消息
     * @return 日志行列表
     */
    public List<String> queryRange(String serverName, LocalDateTime start, LocalDateTime end, String msg) {
        String m = msg == null ? "" : " |= `" + msg + "`";
        
        LokiRangeQuery lokiRangeQuery = new LokiRangeQuery();
        lokiRangeQuery.setQuery("{app=\"" + serverName + "\"} " + m);
        lokiRangeQuery.setLimit(properties.getDefaultLimit());
        lokiRangeQuery.setStart(start.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond());
        lokiRangeQuery.setEnd(end.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond());
        
        LokiLog lokiLog = queryRange(lokiRangeQuery);
        
        List<LokiLog.Data.Result> result = lokiLog.getData().getResult();
        if (result == null || result.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<String> lines = new ArrayList<>();
        for (LokiLog.Data.Result r : result) {
            if (r.getValues() != null) {
                for (List<String> value : r.getValues()) {
                    if (value.size() > 1) {
                        lines.add(value.get(1));
                    }
                }
            }
        }
        
        return lines;
    }

    /**
     * 获取错误消息DTO列表
     *
     * @param serverName 服务名称
     * @param start      开始时间
     * @param end        结束时间
     * @param msg        搜索消息
     * @return 错误消息DTO列表
     */
    public List<ErrorMsgDTO> getErrorMsgDTO(String serverName, LocalDateTime start, LocalDateTime end, String msg) {
        List<String> lines = queryRange(serverName, start, end, msg);
        List<ErrorMsgDTO> list = new ArrayList<>();
        
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            if (line.contains(msg)) {
                ErrorMsgDTO errorMsgDTO = new ErrorMsgDTO();
                
                // 收集相关日志（当前行及后续几行）
                List<String> logs = grep_A(lines, i, 3);
                errorMsgDTO.setLogs(logs);
                
                // 提取TraceId
                for (String log : logs) {
                    Matcher matcher = TRACE_ID_PATTERN.matcher(log);
                    if (matcher.find()) {
                        String traceId = matcher.group(1);
                        errorMsgDTO.setTraceId(traceId);
                        break;
                    }
                }
                
                errorMsgDTO.setTime(LocalDateTime.now()); // 简化时间处理
                list.add(errorMsgDTO);
            }
        }
        
        return list;
    }

    /**
     * 检查应用是否有异常
     *
     * @param appName 应用名称
     * @param minutes 检查最近几分钟
     * @return 是否有异常
     */
    public boolean hasException(String appName, int minutes) {
        LokiRangeQuery query = new LokiRangeQuery();
        query.setQuery("{app=\"" + appName + "\"} |= `Exception`");
        query.setStart(System.currentTimeMillis() / 1000 - 60L * minutes);
        query.setLimit(10);
        query.setDirection(properties.getDefaultDirection());
        
        LokiLog lokiLog = queryRange(query);
        
        if (!lokiLog.getStatus().equals("success")) {
            return false;
        }
        
        return lokiLog.getData().getStats().getSummary().getTotalEntriesReturned() > 0;
    }

    /**
     * 检查应用是否有特定日志
     *
     * @param appName     应用名称
     * @param searchText  搜索文本
     * @param minutes     检查最近几分钟
     * @return 是否有匹配的日志
     */
    public boolean hasLogContaining(String appName, String searchText, int minutes) {
        LokiRangeQuery query = new LokiRangeQuery();
        query.setQuery("{app=\"" + appName + "\"} |= `" + searchText + "`");
        query.setStart(System.currentTimeMillis() / 1000 - 60L * minutes);
        query.setLimit(10);
        query.setDirection(properties.getDefaultDirection());
        
        LokiLog lokiLog = queryRange(query);
        
        if (!lokiLog.getStatus().equals("success")) {
            return false;
        }
        
        return lokiLog.getData().getStats().getSummary().getTotalEntriesReturned() > 0;
    }

    /**
     * 模拟grep -A命令，获取匹配行及其后续几行
     */
    private List<String> grep_A(List<String> lines, int matchIndex, int afterLines) {
        List<String> result = new ArrayList<>();
        int endIndex = Math.min(matchIndex + afterLines + 1, lines.size());
        
        for (int i = matchIndex; i < endIndex; i++) {
            result.add(lines.get(i));
        }
        
        return result;
    }
}
