.App {
  text-align: center;
}

.dashboard-header {
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  margin: 0;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-online {
  background-color: #52c41a;
}

.status-offline {
  background-color: #ff4d4f;
}

.status-warning {
  background-color: #faad14;
}

.chart-container {
  height: 300px;
  padding: 16px;
}

.metric-card {
  text-align: center;
}

.metric-value {
  font-size: 32px;
  font-weight: 600;
  color: #1890ff;
  margin: 8px 0;
}

.metric-label {
  color: #666;
  font-size: 14px;
}

.metric-change {
  font-size: 12px;
  margin-top: 4px;
}

.metric-up {
  color: #52c41a;
}

.metric-down {
  color: #ff4d4f;
}

.alert-item {
  border-left: 4px solid #ff4d4f;
  padding-left: 12px;
  margin-bottom: 12px;
}

.alert-item.warning {
  border-left-color: #faad14;
}

.alert-item.info {
  border-left-color: #1890ff;
} 