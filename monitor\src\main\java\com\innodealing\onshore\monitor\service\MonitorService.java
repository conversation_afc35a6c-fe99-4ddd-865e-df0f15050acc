package com.innodealing.onshore.monitor.service;

import com.innodealing.onshore.monitor.Monitor;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.model.MonitorInfo;

import java.util.List;

public interface MonitorService {

    MonitorExecuteInfo execute(String monitorName);

    MonitorExecuteInfo execute(Class<? extends Monitor> monitorClass);

    MonitorExecuteInfo execute(String monitorName, boolean async, boolean alarm);

    List<MonitorExecuteInfo> executeGroups(String[] groups);

    List<MonitorExecuteInfo> executeAll();

    String generateJobId();

    void schedule(String monitorName);

    void schedules(String[] monitorNames);

    void schedulesAll();

    List<MonitorExecuteInfo> jobs(String... jobIds);

    List<MonitorExecuteInfo> jobsByMonitor(String... monitors);

    /**
     * 获取job执行信息
     *
     * @param jobId jobId
     * @return {@link MonitorExecuteInfo}
     */
    MonitorExecuteInfo getJobInfo(String jobId);

    /**
     * 获取对应monitor的执行信息
     *
     * @param monitorName monitor
     * @return {@link MonitorExecuteInfo} 列表
     */
    List<MonitorExecuteInfo> listMonitorInfos(String monitorName);
}
