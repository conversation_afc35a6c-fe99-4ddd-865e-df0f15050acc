package com.innodealing.loki.sdk.model;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 错误消息DTO
 */
public class ErrorMsgDTO {
    private String traceId;
    private List<String> logs;
    private LocalDateTime time;

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public List<String> getLogs() {
        return logs;
    }

    public void setLogs(List<String> logs) {
        this.logs = logs;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    /**
     * 获取Loki链接
     */
    public String getLokiLink(String serverName) {
        return getLokiUrl("查看日志", this.traceId, serverName);
    }

    /**
     * 获取格式化的时间字符串
     */
    public String getFormattedTime() {
        if (time == null) {
            return "";
        }
        return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取日志摘要
     */
    public String getLogSummary() {
        if (logs == null || logs.isEmpty()) {
            return "无日志信息";
        }
        
        StringBuilder summary = new StringBuilder();
        for (int i = 0; i < Math.min(logs.size(), 3); i++) {
            if (i > 0) {
                summary.append("\n");
            }
            String log = logs.get(i);
            if (log.length() > 100) {
                summary.append(log.substring(0, 100)).append("...");
            } else {
                summary.append(log);
            }
        }
        
        if (logs.size() > 3) {
            summary.append("\n... 还有 ").append(logs.size() - 3).append(" 条日志");
        }
        
        return summary.toString();
    }

    private static String getLokiUrl(String linkName, String traceId, String serverName) {
        try {
            String params = String.format("{\"datasource\":\"Loki\",\"queries\":[{\"refId\":\"A\",\"editorMode\":\"builder\",\"expr\":\"{app=\\\"%s\\\"} |= `%s`\",\"queryType\":\"range\"}]}",
                    serverName, traceId);
            String url = String.format("http://172.16.5.162:30300/explore?orgId=1&left=%s", URLEncoder.encode(params, "utf-8"));
            return String.format("[%s](%s)", linkName, url);
        } catch (UnsupportedEncodingException e) {
            return linkName;
        }
    }

    @Override
    public String toString() {
        return "ErrorMsgDTO{" +
                "traceId='" + traceId + '\'' +
                ", logs=" + logs +
                ", time=" + time +
                '}';
    }
}
