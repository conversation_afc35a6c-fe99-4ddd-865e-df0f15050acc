package com.innodealing.skywalking.sdk.module;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * SkyWalking错误数据详情
 */
public class SkywalkingErrorDataDetail {

    @JsonProperty("spans")
    private List<SpansDTO> spans;

    public List<SpansDTO> getSpans() {
        return spans;
    }

    public void setSpans(List<SpansDTO> spans) {
        this.spans = spans;
    }

    public static class SpansDTO {
        @JsonProperty("traceId")
        private String traceId;
        @JsonProperty("segmentId")
        private String segmentId;
        @JsonProperty("spanId")
        private Integer spanId;
        @JsonProperty("parentSpanId")
        private Integer parentSpanId;
        @JsonProperty("refs")
        private List<?> refs;
        @JsonProperty("serviceCode")
        private String serviceCode;
        @JsonProperty("startTime")
        private Long startTime;
        @JsonProperty("endTime")
        private Long endTime;
        @JsonProperty("endpointName")
        private String endpointName;
        @JsonProperty("type")
        private String type;
        @JsonProperty("peer")
        private String peer;
        @JsonProperty("component")
        private String component;
        @JsonProperty("isError")
        private Boolean isError;
        @JsonProperty("layer")
        private String layer;
        @JsonProperty("tags")
        private List<KeyValueDTO> tags;
        @JsonProperty("logs")
        private List<LogsDTO> logs;

        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }

        public String getSegmentId() {
            return segmentId;
        }

        public void setSegmentId(String segmentId) {
            this.segmentId = segmentId;
        }

        public Integer getSpanId() {
            return spanId;
        }

        public void setSpanId(Integer spanId) {
            this.spanId = spanId;
        }

        public Integer getParentSpanId() {
            return parentSpanId;
        }

        public void setParentSpanId(Integer parentSpanId) {
            this.parentSpanId = parentSpanId;
        }

        public List<?> getRefs() {
            return Objects.isNull(refs) ? new ArrayList<>() : new ArrayList<>(refs);
        }

        public void setRefs(List<?> refs) {
            this.refs = Objects.isNull(refs) ? new ArrayList<>() : new ArrayList<>(refs);
        }

        public String getServiceCode() {
            return serviceCode;
        }

        public void setServiceCode(String serviceCode) {
            this.serviceCode = serviceCode;
        }

        public Long getStartTime() {
            return startTime;
        }

        public void setStartTime(Long startTime) {
            this.startTime = startTime;
        }

        public Long getEndTime() {
            return endTime;
        }

        public void setEndTime(Long endTime) {
            this.endTime = endTime;
        }

        public String getEndpointName() {
            return endpointName;
        }

        public void setEndpointName(String endpointName) {
            this.endpointName = endpointName;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getPeer() {
            return peer;
        }

        public void setPeer(String peer) {
            this.peer = peer;
        }

        public String getComponent() {
            return component;
        }

        public void setComponent(String component) {
            this.component = component;
        }

        public Boolean getError() {
            return isError;
        }

        public void setError(Boolean error) {
            isError = error;
        }

        public String getLayer() {
            return layer;
        }

        public void setLayer(String layer) {
            this.layer = layer;
        }

        public List<KeyValueDTO> getTags() {
            return Objects.isNull(tags) ? new ArrayList<>() : new ArrayList<>(tags);
        }

        public void setTags(List<KeyValueDTO> tags) {
            this.tags = Objects.isNull(tags) ? new ArrayList<>() : new ArrayList<>(tags);
        }

        public List<LogsDTO> getLogs() {
            return Objects.isNull(logs) ? new ArrayList<>() : new ArrayList<>(logs);
        }

        public void setLogs(List<LogsDTO> logs) {
            this.logs = Objects.isNull(logs) ? new ArrayList<>() : new ArrayList<>(logs);
        }

        public static class LogsDTO {
            @JsonProperty("time")
            private Long time;
            @JsonProperty("data")
            private List<KeyValueDTO> data;

            public Long getTime() {
                return time;
            }

            public void setTime(Long time) {
                this.time = time;
            }

            public List<KeyValueDTO> getData() {
                return Objects.isNull(data) ? new ArrayList<>() : new ArrayList<>(data);
            }

            public void setData(List<KeyValueDTO> data) {
                this.data = Objects.isNull(data) ? new ArrayList<>() : new ArrayList<>(data);
            }
        }
    }

    public static class KeyValueDTO {
        @JsonProperty("key")
        private String key;
        @JsonProperty("value")
        private String value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
