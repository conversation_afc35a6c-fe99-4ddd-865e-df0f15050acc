package com.datafeed.monitor.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * 统计查询响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StatisticsResponse {
    
    /**
     * 总发送条数
     */
    private Long totalSendCount;
    
    /**
     * 总数据量（字节）
     */
    private Long totalDataVolume;
    
    /**
     * 总成功条数
     */
    private Long totalSuccessCount;
    
    /**
     * 总失败条数
     */
    private Long totalFailureCount;
    
    /**
     * 平均发送速率（条/单位时间）
     */
    private Double avgSendRate;
    
    /**
     * 峰值发送速率（条/单位时间）
     */
    private Long peakSendRate;
    
    /**
     * 成功率（%）
     */
    private Double successRate;
    
    /**
     * 平均响应时间（毫秒）
     */
    private Double avgResponseTime;
    
    /**
     * 统计单位
     */
    private String timeUnit;
    
    /**
     * 时间序列数据点
     */
    private List<DataPoint> dataPoints;
    
    /**
     * 数据点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DataPoint {
        /**
         * 时间戳
         */
        private String timestamp;
        
        /**
         * 发送条数
         */
        private Long sendCount;
        
        /**
         * 数据量（字节）
         */
        private Long dataVolume;
        
        /**
         * 成功条数
         */
        private Long successCount;
        
        /**
         * 失败条数
         */
        private Long failureCount;
        
        /**
         * 响应时间（毫秒）
         */
        private Double responseTime;
    }
}
