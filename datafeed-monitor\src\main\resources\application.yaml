spring:
  config:
    import:
        - classpath:application-datasource.yaml
  application:
    name: datafeed-monitor
  profiles:
    active: local

server:
  port: '8082'
  servlet:
    context-path: /datafeed-monitor

logging:
  level:
    com:
      datafeed:
        monitor: DEBUG
    org:
      springframework:
        jdbc:
          core:
            JdbcTemplate: DEBUG

# DataFeed Monitor 配置
datafeed:
  monitor:
    schedule:
      enable: 'true'
    store:
      db: ./datafeed-store.db
    statistics:
      # 数据统计配置
      retention:
        days: 30  # 数据保留天数
      aggregation:
        intervalMinutes: 1  # 聚合间隔（分钟）
    redis:
      # Redis 配置用于数据缓存
      enabled: true
      keyPrefix: "datafeed:monitor:"
      ttl: 3600  # 缓存TTL（秒）
