datafeed对外监控平台:

需要具备的功能:
 账号在线连接信息
    账号
    在线状态
    上次登录时间
    上次心跳/数据发送时间
    在线时长
 数据发送统计
    各个模板数据发送数量
    各个模板发送异常占比
 账号权限
    模板权限
    数据方案权限
    每个产品发送数量
账号连接配置信息查询
    账号连接配置信息查询
账号连接异常和数据发送异常告警
    连接异常
    发送失败告警







----------------我是一条分割线-----------------
实现扩展:
 账号在线连接信息
    账号 表
    在线状态 日志/接口查询
    上次登录时间 日志
    上次心跳/数据发送时间 日志
    在线时长 计算 当前-上次登录时间
 数据发送统计
    各个模板数据发送数量 日志
    各个模板发送异常占比 日志
 账号权限
    模板权限 表
    数据方案权限 表
    每个产品发送数量 日志
账号连接配置信息查询
    账号连接配置信息查询 文件读取/接口查询
账号连接异常和数据发送异常告警
    连接异常 日志收集/接口查询 发送至飞书/运维新建平台
    发送失败告警 日志收集/接口查询 发送至飞书/运维新建平台