import api from './api';

// 数据类型定义
export interface TrendDataPoint {
  timePoint: string;
  count: number;
}

export interface ProductStatistics {
  productCode: string;
  count: number;
  percentage: number;
}

export interface DashboardData {
  onlineUserCount: number;
  onlineUsers: string[];
  todaySendCount: number;
  failureCount: number;
  successRate: number;
  trendData: TrendDataPoint[];
  productStatistics: ProductStatistics[];
}

export interface QueryRequest {
  timeUnit: 'minute' | 'second';
  startTime: string;
  endTime: string;
  productCode?: string;
  userAccount?: string;
}

export interface OnlineUsersResponse {
  count: number;
  users: string[];
}

export interface TodayStatistics {
  sendCount: number;
  failureCount: number;
  successCount: number;
  successRate: number;
}

// Datafeed监控服务
export class DatafeedService {
  
  // 获取仪表板数据
  static async getDashboardData(): Promise<DashboardData> {
    return api.get('/api/datafeed/dashboard');
  }

  // 获取指定时间范围的仪表板数据
  static async getDashboardDataByRange(startTime: string, endTime: string): Promise<DashboardData> {
    return api.get('/api/datafeed/dashboard/range', {
      params: { startTime, endTime }
    });
  }

  // 获取发送趋势数据
  static async getSendTrend(request: QueryRequest): Promise<TrendDataPoint[]> {
    return api.post('/api/datafeed/trend', request);
  }

  // 获取实时趋势数据
  static async getRealtimeTrend(timeUnit: 'minute' | 'second' = 'minute', minutes: number = 60): Promise<TrendDataPoint[]> {
    return api.get('/api/datafeed/trend/realtime', {
      params: { timeUnit, minutes }
    });
  }

  // 获取产品统计数据
  static async getProductStatistics(startTime?: string, endTime?: string): Promise<ProductStatistics[]> {
    const params: any = {};
    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;
    
    return api.get('/api/datafeed/products/statistics', { params });
  }

  // 获取在线用户
  static async getOnlineUsers(): Promise<OnlineUsersResponse> {
    return api.get('/api/datafeed/users/online');
  }

  // 获取所有用户
  static async getAllUsers(): Promise<string[]> {
    return api.get('/api/datafeed/users/all');
  }

  // 获取今日统计数据
  static async getTodayStatistics(): Promise<TodayStatistics> {
    return api.get('/api/datafeed/statistics/today');
  }

  // 健康检查
  static async healthCheck(): Promise<any> {
    return api.get('/api/datafeed/health');
  }
}

export default DatafeedService;
