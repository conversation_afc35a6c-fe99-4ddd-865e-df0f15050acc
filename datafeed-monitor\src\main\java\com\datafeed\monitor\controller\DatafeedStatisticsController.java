package com.datafeed.monitor.controller;

import com.datafeed.monitor.model.DatafeedStatistics;
import com.datafeed.monitor.model.StatisticsRequest;
import com.datafeed.monitor.model.StatisticsResponse;
import com.datafeed.monitor.service.DatafeedStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 数据发送统计控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/statistics")
@Validated
public class DatafeedStatisticsController {
    
    @Autowired
    private DatafeedStatisticsService statisticsService;
    
    @PostMapping("/record")
    public ResponseEntity<Map<String, Object>> recordStatistics(
            @RequestBody @Valid DatafeedStatistics statistics) {
        try {
            // 设置创建时间
            statistics.setCreateTime(new Timestamp(System.currentTimeMillis()));
            
            statisticsService.recordStatistics(statistics);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "统计数据记录成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("记录统计数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "统计数据记录失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    @PostMapping("/batch-record")
    public ResponseEntity<Map<String, Object>> batchRecordStatistics(
            @RequestBody @Valid List<DatafeedStatistics> statisticsList) {
        try {
            // 设置创建时间
            Timestamp now = new Timestamp(System.currentTimeMillis());
            statisticsList.forEach(stats -> stats.setCreateTime(now));
            
            statisticsService.batchRecordStatistics(statisticsList);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量统计数据记录成功");
            response.put("count", statisticsList.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量记录统计数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量统计数据记录失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    @PostMapping("/query")
    public ResponseEntity<StatisticsResponse> getStatistics(
            @RequestBody @Valid StatisticsRequest request) {
        try {
            StatisticsResponse response = statisticsService.getStatistics(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询统计数据失败", e);
            return ResponseEntity.ok(StatisticsResponse.builder()
                    .timeUnit(request.getTimeUnit())
                    .build());
        }
    }
    
    @GetMapping("/realtime")
    public ResponseEntity<StatisticsResponse> getRealtimeStatistics(
            @RequestParam(defaultValue = "minute") 
            @Pattern(regexp = "^(minute|second)$", message = "统计单位只能是 minute 或 second")
            String timeUnit,
            
            @RequestParam(defaultValue = "60") 
            @Min(value = 1, message = "分钟数必须大于0")
            @Max(value = 1440, message = "分钟数不能超过1440（24小时）")
            int minutes) {
        try {
            StatisticsResponse response = statisticsService.getRealtimeStatistics(timeUnit, minutes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取实时统计数据失败", e);
            return ResponseEntity.ok(StatisticsResponse.builder()
                    .timeUnit(timeUnit)
                    .build());
        }
    }
    
    @GetMapping("/datasources")
    public ResponseEntity<List<String>> getDataSources() {
        try {
            List<String> dataSources = statisticsService.getDataSources();
            return ResponseEntity.ok(dataSources);
        } catch (Exception e) {
            log.error("获取数据源列表失败", e);
            return ResponseEntity.ok(new ArrayList<>());
        }
    }
    
    @GetMapping("/target-systems")
    public ResponseEntity<List<String>> getTargetSystems() {
        try {
            List<String> targetSystems = statisticsService.getTargetSystems();
            return ResponseEntity.ok(targetSystems);
        } catch (Exception e) {
            log.error("获取目标系统列表失败", e);
            return ResponseEntity.ok(new ArrayList<>());
        }
    }
    
    @DeleteMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupExpiredData(
            @RequestParam(defaultValue = "30") 
            @Min(value = 1, message = "保留天数必须大于0")
            @Max(value = 365, message = "保留天数不能超过365天")
            int retentionDays) {
        try {
            int cleanedCount = statisticsService.cleanupExpiredData(retentionDays);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "数据清理完成");
            response.put("cleanedCount", cleanedCount);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("清理过期数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "数据清理失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "datafeed-monitor");
        response.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(response);
    }
}