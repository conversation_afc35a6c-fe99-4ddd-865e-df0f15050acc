-- 创建datafeed_monitor数据库
CREATE DATABASE IF NOT EXISTS datafeed_monitor DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE datafeed_monitor;

-- datafeed模板表
CREATE TABLE IF NOT EXISTS datafeed_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(50) COMMENT '产品代码',
    default_template_status INT COMMENT '默认模板状态',
    quickfix_keys VARCHAR(2000) COMMENT 'QuickFIX键配置',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_product_code (product_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='datafeed模板表';

-- 用户模板配置表
CREATE TABLE IF NOT EXISTS datafeed_user_template_cfg (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_account VARCHAR(50) COMMENT '用户账号',
    template_id BIGINT COMMENT '模板ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_account (user_account),
    INDEX idx_template_id (template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户模板配置表';

-- datafeed用户筛选方案表
CREATE TABLE IF NOT EXISTS datafeed_user_filter_schema (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_account VARCHAR(50) COMMENT '用户账号',
    template_id BIGINT COMMENT '模板ID',
    schema_content TEXT COMMENT '方案内容',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_account (user_account),
    INDEX idx_template_id (template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='datafeed用户筛选方案表';

-- datafeed原始消息表
CREATE TABLE IF NOT EXISTS datafeed_origin_message (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(50) COMMENT '产品代码',
    message_seq VARCHAR(200) COMMENT '消息序列号',
    send_time DATETIME COMMENT '发送时间',
    queue_hash_key VARCHAR(200) COMMENT '队列哈希键',
    message_content TEXT COMMENT '消息内容',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_product_code (product_code),
    INDEX idx_create_time (create_time),
    INDEX idx_send_time (send_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='datafeed原始消息表';

-- 插入示例数据
INSERT IGNORE INTO datafeed_template (product_code, default_template_status, quickfix_keys) VALUES
('BOND', 1, '8=FIX.4.2|9=|35=D|49=|56=|34=|52=|11=|21=1|55=|54=|38=|40=2|44=|59=0|60=|10='),
('EQUITY', 1, '8=FIX.4.2|9=|35=D|49=|56=|34=|52=|11=|21=1|55=|54=|38=|40=1|44=|59=0|60=|10='),
('FOREX', 1, '8=FIX.4.2|9=|35=D|49=|56=|34=|52=|11=|21=1|55=|54=|38=|40=2|44=|59=0|60=|10=');

INSERT IGNORE INTO datafeed_user_template_cfg (user_account, template_id) VALUES
('user001', 1),
('user002', 2),
('user003', 3),
('admin', 1);

-- 插入示例消息数据（用于测试）
INSERT IGNORE INTO datafeed_origin_message (product_code, message_seq, send_time, queue_hash_key, message_content) VALUES
('BOND', 'MSG001', NOW() - INTERVAL 1 HOUR, 'hash001', '8=FIX.4.2|9=178|35=D|49=SENDER|56=TARGET|34=1|52=********-10:00:00|11=ORDER001|21=1|55=BOND001|54=1|38=1000|40=2|44=100.50|59=0|60=********-10:00:00|10=123|'),
('EQUITY', 'MSG002', NOW() - INTERVAL 30 MINUTE, 'hash002', '8=FIX.4.2|9=178|35=D|49=SENDER|56=TARGET|34=2|52=********-10:30:00|11=ORDER002|21=1|55=EQUITY001|54=2|38=500|40=1|44=50.25|59=0|60=********-10:30:00|10=124|'),
('FOREX', 'MSG003', NOW() - INTERVAL 15 MINUTE, 'hash003', '8=FIX.4.2|9=178|35=D|49=SENDER|56=TARGET|34=3|52=********-10:45:00|11=ORDER003|21=1|55=EURUSD|54=1|38=10000|40=2|44=1.0850|59=0|60=********-10:45:00|10=125|');
