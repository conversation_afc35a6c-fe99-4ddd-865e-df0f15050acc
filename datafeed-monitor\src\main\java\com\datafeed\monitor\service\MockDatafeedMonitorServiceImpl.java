package com.datafeed.monitor.service;

import com.datafeed.monitor.model.DatafeedDashboardData;
import com.datafeed.monitor.model.DatafeedQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 模拟数据的DataFeed监控服务实现
 */
@Slf4j
@Service
@Primary
public class MockDatafeedMonitorServiceImpl implements DatafeedMonitorService {
    
    private final Random random = new Random();
    private final List<String> productCodes = Arrays.asList("BOND", "EQUITY", "FOREX", "COMMODITY", "CRYPTO");
    private final List<String> userAccounts = Arrays.asList(
        "user001", "user002", "user003", "user004", "user005",
        "user006", "user007", "user008", "user009", "user010"
    );
    
    @Override
    public DatafeedDashboardData getDashboardData() {
        return getDashboardData(LocalDateTime.now().minusHours(24), LocalDateTime.now());
    }
    
    @Override
    public DatafeedDashboardData getDashboardData(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("获取仪表板数据: {} - {}", startTime, endTime);
        
        // 生成模拟的在线用户
        List<String> onlineUsers = new ArrayList<>();
        int onlineCount = random.nextInt(8) + 3; // 3-10个在线用户
        for (int i = 0; i < onlineCount; i++) {
            onlineUsers.add(userAccounts.get(i));
        }
        
        // 生成模拟的趋势数据
        List<DatafeedDashboardData.TrendDataPoint> trendData = generateTrendData(startTime, endTime, "minute");
        
        // 生成模拟的产品统计
        List<DatafeedDashboardData.ProductStatistics> productStats = generateProductStatistics();
        
        return DatafeedDashboardData.builder()
                .onlineUserCount(onlineUsers.size())
                .onlineUsers(onlineUsers)
                .todaySendCount((long) calculateTotalCount(trendData))
                .failureCount((long) (random.nextInt(50) + 10))
                .successRate(95.0 + random.nextDouble() * 4) // 95-99%
                .trendData(trendData)
                .productStatistics(productStats)
                .build();
    }
    
    @Override
    public List<DatafeedDashboardData.TrendDataPoint> getSendTrend(DatafeedQueryRequest request) {
        log.info("获取发送趋势: {}", request);
        return generateTrendData(request.getStartTime(), request.getEndTime(), request.getTimeUnit());
    }
    
    @Override
    public List<DatafeedDashboardData.ProductStatistics> getProductStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("获取产品统计: {} - {}", startTime, endTime);
        return generateProductStatistics();
    }
    
    @Override
    public List<String> getOnlineUsers() {
        List<String> onlineUsers = new ArrayList<>();
        int onlineCount = random.nextInt(8) + 3; // 3-10个在线用户
        for (int i = 0; i < onlineCount; i++) {
            onlineUsers.add(userAccounts.get(i));
        }
        return onlineUsers;
    }
    
    @Override
    public List<String> getAllUsers() {
        return new ArrayList<>(userAccounts);
    }
    
    @Override
    public Long getTodaySendCount() {
        return (long) (random.nextInt(50000) + 10000); // 10000-60000
    }
    
    @Override
    public Long getFailureCount(LocalDateTime startTime, LocalDateTime endTime) {
        return (long) (random.nextInt(500) + 50); // 50-550
    }
    
    /**
     * 生成模拟趋势数据
     */
    private List<DatafeedDashboardData.TrendDataPoint> generateTrendData(
            LocalDateTime startTime, LocalDateTime endTime, String timeUnit) {
        
        List<DatafeedDashboardData.TrendDataPoint> trendData = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        LocalDateTime current = startTime;
        while (current.isBefore(endTime) || current.isEqual(endTime)) {
            int baseCount = 100 + random.nextInt(200); // 100-300基础值
            // 添加一些波动
            double wave = Math.sin(current.getHour() * Math.PI / 12) * 50; // 按小时的波动
            int count = (int) (baseCount + wave + random.nextGaussian() * 20);
            count = Math.max(count, 10); // 最小值10
            
            trendData.add(DatafeedDashboardData.TrendDataPoint.builder()
                    .timePoint(current.format(formatter))
                    .count((long) count)
                    .build());
            
            // 根据时间单位递增
            if ("second".equals(timeUnit)) {
                current = current.plusSeconds(30); // 每30秒一个点
            } else {
                current = current.plusMinutes(5); // 每5分钟一个点
            }
        }
        
        return trendData;
    }
    
    /**
     * 生成模拟产品统计数据
     */
    private List<DatafeedDashboardData.ProductStatistics> generateProductStatistics() {
        List<DatafeedDashboardData.ProductStatistics> stats = new ArrayList<>();
        
        int totalCount = 0;
        List<Integer> counts = new ArrayList<>();
        
        // 先生成各产品的数量
        for (String productCode : productCodes) {
            int count = random.nextInt(5000) + 1000; // 1000-6000
            counts.add(count);
            totalCount += count;
        }
        
        // 计算百分比并创建统计对象
        for (int i = 0; i < productCodes.size(); i++) {
            String productCode = productCodes.get(i);
            int count = counts.get(i);
            double percentage = (double) count / totalCount * 100;
            
            stats.add(DatafeedDashboardData.ProductStatistics.builder()
                    .productCode(productCode)
                    .count((long) count)
                    .percentage(Math.round(percentage * 100.0) / 100.0) // 保留2位小数
                    .build());
        }
        
        return stats;
    }
    
    /**
     * 计算趋势数据的总数量
     */
    private int calculateTotalCount(List<DatafeedDashboardData.TrendDataPoint> trendData) {
        return trendData.stream()
                .mapToInt(point -> point.getCount().intValue())
                .sum();
    }
}
