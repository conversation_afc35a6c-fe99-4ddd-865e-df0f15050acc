spring:
  config:
    import:
        - classpath:application-skywalking.yaml
        - classpath:application-datasource.yaml
        - classpath:application-sdk-loki.yaml
        - classpath:application-sdk-skywalking.yaml
  application:
    name: monitor
  profiles:
    active: local

server:
  port: '8081'
  servlet:
    context-path: /monitor

logging:
  level:
    com:
      innodealing:
        onshore:
          monitor: DEBUG
    org:
      springframework:
        jdbc:
          core:
            JdbcTemplate: DEBUG

feishu:
  robot:
    security: FGBkz9SpM8Or9O7shdEV7c
    token: fefafe27-4f01-4f61-8ea4-5a265dbfe91c

monitor:
  schedule:
    enable: 'true'
  store:
    db: ./store.db
  retry:
    maxCount: 2
    delaySeconds: 30
# Loki SDK配置 - 暂时禁用
loki:
  enabled: false
  url: http://172.16.5.162:30100
  defaultLimit: 1000
  defaultDirection: backward
  connectTimeout: 30000
  readTimeout: 30000