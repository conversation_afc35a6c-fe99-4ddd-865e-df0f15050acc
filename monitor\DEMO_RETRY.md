# Monitor监控服务重试功能演示

## 功能演示

本文档演示了Monitor监控服务的重试功能，展示了当监控任务超时或执行异常时，系统如何自动进行重试。

## 重试功能特性

### 1. 自动重试机制
- **触发条件**：任务超时（120秒）或执行异常
- **重试次数**：默认2次（可配置）
- **重试间隔**：默认30秒（可配置）
- **状态跟踪**：完整记录重试历史

### 2. 配置参数
```yaml
monitor:
  retry:
    maxCount: 2        # 最大重试次数
    delaySeconds: 30   # 重试间隔（秒）
```

## 演示场景

### 场景1：执行异常后成功重试

```java
// 模拟监控器：前两次失败，第三次成功
@MonitorClient(desc = "重试演示监控器")
public class RetryDemoMonitor implements Monitor {
    private static AtomicInteger callCount = new AtomicInteger(0);
    
    @Override
    public MonitorReport reportMonitor() {
        int count = callCount.incrementAndGet();
        
        if (count <= 2) {
            // 前两次调用模拟失败
            throw new RuntimeException("模拟失败 - 第" + count + "次调用");
        }
        
        // 第三次调用成功
        return new MonitorReport(true, DefaultReport.success());
    }
}
```

### 场景2：达到最大重试次数后告警

```java
// 模拟监控器：始终失败，触发告警
@MonitorClient(desc = "失败演示监控器")
public class FailDemoMonitor implements Monitor {
    @Override
    public MonitorReport reportMonitor() {
        // 始终抛出异常
        throw new RuntimeException("模拟持续失败");
    }
}
```

## 执行结果示例

### 成功重试的执行结果

```
=== 监控执行结果 ===
任务ID: JOB_20250616_001
任务描述: 重试演示监控器
最终状态: 200 (SUCCESS)
重试次数: 2
最大重试次数: 2
总调用次数: 3 (初始1次 + 重试2次)
监控最终结果: 成功

=== 重试历史详情 ===
重试 1: 时间=2025-06-16 10:30:15, 原因=Task timeout, attempting retry, 结果=502
重试 2: 时间=2025-06-16 10:30:45, 原因=Task timeout, attempting retry, 结果=200
```

### 重试失败的执行结果

```
=== 监控执行结果 ===
任务ID: JOB_20250616_002
任务描述: 失败演示监控器
最终状态: 502 (EXCEPTION)
重试次数: 2
最大重试次数: 2
总调用次数: 3 (初始1次 + 重试2次)
监控最终结果: 失败

=== 重试历史详情 ===
重试 1: 时间=2025-06-16 10:35:15, 原因=Task timeout, attempting retry, 结果=502
重试 2: 时间=2025-06-16 10:35:45, 原因=Task timeout, attempting retry, 结果=502

⚠️ 告警已触发：监控任务在重试2次后仍然失败
```

## 日志输出示例

```
2025-06-16 10:30:15.123 INFO  [pool-1-thread-1] MonitorServiceImpl - Starting retry 1 for jobId:JOB_20250616_001, delaying 30 seconds
2025-06-16 10:30:45.456 INFO  [pool-1-thread-1] MonitorServiceImpl - Starting retry 2 for jobId:JOB_20250616_001, delaying 30 seconds
2025-06-16 10:31:15.789 INFO  [pool-1-thread-1] MonitorServiceImpl - Monitor execution successful after 2 retries, jobId:JOB_20250616_001
```

## 状态码说明

| 状态码 | 名称 | 说明 |
|--------|------|------|
| 0 | RUNNING | 运行中 |
| 100 | RETRYING | 重试中 |
| 200 | SUCCESS | 成功 |
| 500 | FAIL | 失败 |
| 502 | EXCEPTION | 异常 |

## 重试流程图

```
开始执行监控任务
    ↓
执行监控逻辑
    ↓
成功？ ──是──→ 返回SUCCESS
    ↓ 否
超时/异常？
    ↓ 是
重试次数 < 最大重试次数？
    ↓ 是
设置状态为RETRYING
    ↓
记录重试信息
    ↓
延迟30秒
    ↓
重新执行监控任务
    ↓
成功？ ──是──→ 返回SUCCESS
    ↓ 否
继续重试...
    ↓
达到最大重试次数
    ↓
设置状态为EXCEPTION
    ↓
触发告警
    ↓
结束
```

## 实际运行测试

要测试重试功能，可以运行以下测试：

```bash
# 运行重试功能测试
mvn test -Dtest=RetryFeatureTest

# 运行配置测试
mvn test -Dtest=RetryFeatureTest#testRetryConfiguration
```

## 监控指标

通过重试功能，您可以监控以下指标：

1. **重试成功率**：重试后成功的任务比例
2. **平均重试次数**：任务平均需要重试的次数
3. **重试延迟时间**：重试导致的额外执行时间
4. **最终失败率**：重试后仍然失败的任务比例

## 最佳实践

1. **合理设置重试次数**：建议2-3次，避免过多重试影响性能
2. **适当的重试间隔**：建议30-60秒，给系统恢复时间
3. **监控重试指标**：定期分析重试数据，识别系统问题
4. **告警策略优化**：只在重试失败后发送告警，减少噪音

## 注意事项

- 重试会增加任务总执行时间
- 重试过程中会消耗额外的系统资源
- 告警会有延迟（重试时间 × 重试次数）
- 所有重试过程都会记录详细日志

## 版本信息

- **功能版本**：v1.1.0
- **测试状态**：✅ 通过
- **兼容性**：向后兼容
- **文档更新**：2025-06-16
