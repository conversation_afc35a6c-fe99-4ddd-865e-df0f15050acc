package com.innodealing.skywalking.sdk.module;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * SkyWalking错误数据
 */
public class SkywalkingErrorData {

    @JsonProperty("data")
    private List<DataDTO> data;
    @JsonProperty("total")
    private Integer total;

    public List<DataDTO> getData() {
        return Objects.isNull(data) ? new ArrayList<>() : new ArrayList<>(data);
    }

    public void setData(List<DataDTO> data) {
        this.data = Objects.isNull(data) ? new ArrayList<>() : new ArrayList<>(data);
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public static class DataDTO {
        @JsonProperty("key")
        private String key;
        @JsonProperty("endpointNames")
        private List<String> endpointNames;
        @JsonProperty("duration")
        private Integer duration;
        @JsonProperty("start")
        private String start;
        @JsonProperty("isError")
        private Boolean isError;
        @JsonProperty("traceIds")
        private List<String> traceIds;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public List<String> getEndpointNames() {
            return Objects.isNull(endpointNames) ? new ArrayList<>() : new ArrayList<>(endpointNames);
        }

        public void setEndpointNames(List<String> endpointNames) {
            this.endpointNames = Objects.isNull(endpointNames) ? new ArrayList<>() : new ArrayList<>(endpointNames);
        }

        public Integer getDuration() {
            return duration;
        }

        public void setDuration(Integer duration) {
            this.duration = duration;
        }

        public String getStart() {
            return start;
        }

        public void setStart(String start) {
            this.start = start;
        }

        public Boolean getIsError() {
            return isError;
        }

        public void setIsError(Boolean isError) {
            this.isError = isError;
        }

        public List<String> getTraceIds() {
            return Objects.isNull(traceIds) ? new ArrayList<>() : new ArrayList<>(traceIds);
        }

        public void setTraceIds(List<String> traceIds) {
            this.traceIds = Objects.isNull(traceIds) ? new ArrayList<>() : new ArrayList<>(traceIds);
        }

        @Override
        public String toString() {
            return "DataDTO{" +
                    "key='" + key + '\'' +
                    ", endpointNames=" + endpointNames +
                    ", duration=" + duration +
                    ", start='" + start + '\'' +
                    ", isError=" + isError +
                    ", traceIds=" + traceIds +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "SkywalkingErrorData{" +
                "data=" + data +
                ", total=" + total +
                '}';
    }
}
