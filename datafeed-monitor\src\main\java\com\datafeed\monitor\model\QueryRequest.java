package com.datafeed.monitor.model;

/**
 * 查询请求参数
 */
public class QueryRequest {
    
    /**
     * 时间单位：minute 或 second
     */
    private String timeUnit;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 产品代码（可选）
     */
    private String productCode;
    
    /**
     * 用户账号（可选）
     */
    private String userAccount;
    
    public QueryRequest() {}
    
    public String getTimeUnit() {
        return timeUnit;
    }
    
    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    public String getProductCode() {
        return productCode;
    }
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public String getUserAccount() {
        return userAccount;
    }
    
    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }
    
    @Override
    public String toString() {
        return "QueryRequest{" +
                "timeUnit='" + timeUnit + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", productCode='" + productCode + '\'' +
                ", userAccount='" + userAccount + '\'' +
                '}';
    }
}
