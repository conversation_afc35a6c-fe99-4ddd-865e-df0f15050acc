package com.innodealing.onshore.monitor.config;

import org.springframework.boot.task.TaskSchedulerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 定时任务配置 自定义线程池
 *
 * <AUTHOR>
 */
@Configuration
public class ScheduleConfiguration {

    @Bean
    public ThreadPoolTaskScheduler threadPoolTaskScheduler() {
        return new TaskSchedulerBuilder()
                .poolSize(200)
                .build();
    }
}
