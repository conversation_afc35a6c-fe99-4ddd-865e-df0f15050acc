package com.innodealing.loki.sdk.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Loki SDK配置属性
 */
@ConfigurationProperties(prefix = "loki")
public class LokiProperties {

    /**
     * Loki服务器URL
     */
    private String url = "http://************:30100";

    /**
     * 是否启用Loki监控
     */
    private boolean enabled = true;

    /**
     * 默认查询限制
     */
    private int defaultLimit = 1000;

    /**
     * 默认查询方向
     */
    private String defaultDirection = "backward";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getDefaultLimit() {
        return defaultLimit;
    }

    public void setDefaultLimit(int defaultLimit) {
        this.defaultLimit = defaultLimit;
    }

    public String getDefaultDirection() {
        return defaultDirection;
    }

    public void setDefaultDirection(String defaultDirection) {
        this.defaultDirection = defaultDirection;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
