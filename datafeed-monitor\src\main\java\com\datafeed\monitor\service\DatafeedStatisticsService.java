package com.datafeed.monitor.service;

import com.datafeed.monitor.model.DatafeedStatistics;
import com.datafeed.monitor.model.StatisticsRequest;
import com.datafeed.monitor.model.StatisticsResponse;

import java.util.List;

/**
 * 数据发送统计服务接口
 */
public interface DatafeedStatisticsService {
    
    /**
     * 记录数据发送统计
     * 
     * @param statistics 统计数据
     */
    void recordStatistics(DatafeedStatistics statistics);
    
    /**
     * 批量记录数据发送统计
     * 
     * @param statisticsList 统计数据列表
     */
    void batchRecordStatistics(List<DatafeedStatistics> statisticsList);
    
    /**
     * 查询统计数据
     * 
     * @param request 查询请求
     * @return 统计响应
     */
    StatisticsResponse getStatistics(StatisticsRequest request);
    
    /**
     * 获取实时统计数据
     * 
     * @param timeUnit 时间单位 (minute/second)
     * @param minutes 最近多少分钟的数据
     * @return 统计响应
     */
    StatisticsResponse getRealtimeStatistics(String timeUnit, int minutes);
    
    /**
     * 清理过期数据
     * 
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredData(int retentionDays);
    
    /**
     * 获取数据源列表
     * 
     * @return 数据源列表
     */
    List<String> getDataSources();
    
    /**
     * 获取目标系统列表
     * 
     * @return 目标系统列表
     */
    List<String> getTargetSystems();
}
