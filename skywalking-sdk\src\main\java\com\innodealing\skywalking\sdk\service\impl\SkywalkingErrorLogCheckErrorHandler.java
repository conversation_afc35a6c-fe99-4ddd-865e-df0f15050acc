package com.innodealing.skywalking.sdk.service.impl;

import com.innodealing.skywalking.sdk.module.ErrorLogDTO;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorData;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorDataDetail;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorLogResponse;
import com.innodealing.skywalking.sdk.service.SkywalkingErrorLogHandler;
import com.innodealing.skywalking.sdk.service.SkywalkingHttpService;
import com.innodealing.skywalking.sdk.service.log.AbstractDingdingLogHandler;
import com.innodealing.skywalking.sdk.service.log.AbstractDingdingMarkdownLogHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SkyWalking错误日志检查错误处理器
 */
public class SkywalkingErrorLogCheckErrorHandler implements SkywalkingErrorLogHandler {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final SkywalkingHttpService skywalkingHttpService;

    public SkywalkingErrorLogCheckErrorHandler(String skywalkingUrl) {
        this.skywalkingHttpService = new SkywalkingHttpService(skywalkingUrl);
    }

    @Override
    public Optional<ErrorLogDTO> getErrorLog(String serverName, int serverId, Set<String> endpointNameFilter, LocalDateTime start, LocalDateTime end) throws Exception {
        logger.info("CheckErrorHandler getErrorLog serverId={} serverName={} start={} end={} endpointNameFilter={}", serverId, serverName, start, end, endpointNameFilter);
        SkywalkingErrorLogResponse skywalkingErrorLogResponse = this.getErrorLogResponse(serverName, serverId, endpointNameFilter, start, end);
        for (SkywalkingErrorLogResponse.Detail detail : skywalkingErrorLogResponse.getDetails()) {
            SkywalkingErrorDataDetail skywalkingErrorDataDetail = skywalkingHttpService.getSkywalkingErrorDataDetail(detail.getTraceId());
            Set<String> errorTitleSet = new LinkedHashSet<>();
            Set<String> httpErrorSet = new LinkedHashSet<>();
            Set<String> messageErrorSet = new LinkedHashSet<>();
            for (SkywalkingErrorDataDetail.SpansDTO span : skywalkingErrorDataDetail.getSpans()) {
                if ("Http".equalsIgnoreCase(span.getLayer()) && this.isTrue(span.getError())) {
                    final Map<String, List<SkywalkingErrorDataDetail.KeyValueDTO>> collect = this.getErrorMap(span.getTags());
                    final String url = this.parseErrorString(collect.get("url")).stream().findFirst().orElse("无");
                    final String method = this.parseErrorString(collect.get("http.method")).stream().findFirst().orElse("无");
                    final String status = this.parseErrorString(collect.get("status_code")).stream().findFirst().orElse("无");
                    if (endpointNameFilter.contains(status)) {
                        skywalkingErrorLogResponse.addIgnoreTotal();
                        continue;
                    }
                    errorTitleSet.add("Http请求错误");
                    httpErrorSet.add(String.format("[%s] %s 状态码:%s ", method, url, status));
                    for (SkywalkingErrorDataDetail.SpansDTO.LogsDTO log : span.getLogs()) {
                        final Map<String, List<SkywalkingErrorDataDetail.KeyValueDTO>> errorMap = this.getErrorMap(log.getData());
                        if (!CollectionUtils.isEmpty(errorMap)) {
                            List<SkywalkingErrorDataDetail.KeyValueDTO> keyValueDTOs = errorMap.get("error.kind");
                            if (Objects.isNull(keyValueDTOs)) {
                                continue;
                            }
                            for (SkywalkingErrorDataDetail.KeyValueDTO keyValueDTO : keyValueDTOs) {
                                if (keyValueDTO == null) {
                                    continue;
                                }
                                if (endpointNameFilter.contains(keyValueDTO.getValue())) {
                                    skywalkingErrorLogResponse.addIgnoreTotal();
                                    break;
                                }
                            }
                        }
                        messageErrorSet.addAll(this.parseErrorString(errorMap.get("message")));
                        messageErrorSet.addAll(this.parseErrorString(errorMap.get("stack")));
                    }
                }
                if ("Database".equalsIgnoreCase(span.getLayer()) && this.isTrue(span.getError())) {
                    errorTitleSet.add("数据库发生错误⚠️⚠️⚠️");
                    for (SkywalkingErrorDataDetail.SpansDTO.LogsDTO log : span.getLogs()) {
                        final Map<String, List<SkywalkingErrorDataDetail.KeyValueDTO>> errorMap = this.getErrorMap(log.getData());
                        messageErrorSet.addAll(this.parseErrorString(errorMap.get("message")));
                        messageErrorSet.addAll(this.parseErrorString(errorMap.get("db.statement")));
                    }
                    messageErrorSet.add(span.getTags().stream()
                            .filter(a -> "db.statement".equals(a.getKey())).map(a -> "db.statement: " + a.getValue())
                            .findFirst().orElse(""));
                }
            }
            String httpError = httpErrorSet.stream().collect(Collectors.joining(System.lineSeparator()));
            String messageError = messageErrorSet.stream().collect(Collectors.joining(System.lineSeparator()));
            detail.setErrorTitle(String.join("  ", errorTitleSet));
            detail.setErrorMessage(httpError + messageError);
        }
        AbstractDingdingLogHandler logHandler = new AbstractDingdingMarkdownLogHandler(skywalkingErrorLogResponse);
        return logHandler.handlerLog(start, end);
    }

    private SkywalkingErrorLogResponse getErrorLogResponse(String serverName, int serverId, Set<String> endpointNameFilter, LocalDateTime start, LocalDateTime end) throws Exception {
        SkywalkingErrorData skywalkingErrorData = skywalkingHttpService.getSkywalkingErrorData(serverId, start, end);
        List<SkywalkingErrorData.DataDTO> data = skywalkingErrorData.getData();
        int total = data.size();
        SkywalkingErrorLogResponse skywalkingErrorLogResponse = new SkywalkingErrorLogResponse();
        Set<SkywalkingErrorLogResponse.Detail> details = new LinkedHashSet<>();
        for (SkywalkingErrorData.DataDTO trace : data) {
            for (String endpointName : trace.getEndpointNames()) {
                for (String traceId : trace.getTraceIds()) {
                    if (endpointNameFilter.contains(endpointName)) {
                        logger.warn("filter serverId:{} endpointName:{}", serverId, endpointName);
                        skywalkingErrorLogResponse.addIgnoreTotal();
                        continue;
                    }
                    SkywalkingErrorLogResponse.Detail detail = new SkywalkingErrorLogResponse.Detail();
                    detail.setEndpointName(endpointName);
                    detail.setTraceId(traceId);
                    if (details.contains(detail)) {
                        skywalkingErrorLogResponse.addIgnoreTotal();
                    }
                    details.add(detail);
                }
            }
        }
        skywalkingErrorLogResponse.setDetails(details);
        skywalkingErrorLogResponse.setTotal(total);
        skywalkingErrorLogResponse.setServerName(serverName);
        return skywalkingErrorLogResponse;
    }

    private boolean isTrue(Boolean b) {
        if (b == null) {
            return false;
        }
        return b;
    }

    private Map<String, List<SkywalkingErrorDataDetail.KeyValueDTO>> getErrorMap(List<SkywalkingErrorDataDetail.KeyValueDTO> keyValueDTOs) {
        return keyValueDTOs.stream().collect(Collectors.groupingBy(SkywalkingErrorDataDetail.KeyValueDTO::getKey));
    }

    private List<String> parseErrorString(List<SkywalkingErrorDataDetail.KeyValueDTO> keyValueDTOs) {
        if (CollectionUtils.isEmpty(keyValueDTOs)) {
            return Collections.emptyList();
        }
        return keyValueDTOs.stream().map(SkywalkingErrorDataDetail.KeyValueDTO::getValue).collect(Collectors.toList());
    }
}
