package com.innodealing.loki.sdk.model;

import java.util.Objects;
import java.util.StringJoiner;

/**
 * Loki范围查询日志
 *
 * @see https://grafana.com/docs/loki/latest/reference/loki-http-api/#query-logs-within-a-range-of-time
 */
public class LokiRangeQuery {
    /**
     * The LogQL query to perform
     * 可以copy grafana loki中的expr
     */
    private String query;
    /**
     * 查询条数
     */
    private Integer limit;
    /**
     * 开始时间范围 unix seconds 时间戳
     */
    private Long start;
    /**
     * 结束时间范围 unix seconds 时间戳
     */
    private Long end;
    /**
     * 时间范围开始值的另一种写法 当start有值时这个属性失效
     * 支持duration写法 比如 now-5m now-30s now-1h
     */
    private String since;
    /**
     * 排序方向 支持两个值 forward和backward
     */
    private String direction;

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Long getStart() {
        return start;
    }

    public void setStart(Long start) {
        this.start = start;
    }

    public Long getEnd() {
        return end;
    }

    public void setEnd(Long end) {
        this.end = end;
    }

    public String getSince() {
        return since;
    }

    public void setSince(String since) {
        this.since = since;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    /**
     * 转换为URL查询参数
     */
    public String toUrl() {
        StringJoiner joiner = new StringJoiner("&");
        
        if (query != null) {
            try {
                joiner.add("query=" + java.net.URLEncoder.encode(query, "UTF-8"));
            } catch (java.io.UnsupportedEncodingException e) {
                joiner.add("query=" + query);
            }
        }
        
        if (limit != null) {
            joiner.add("limit=" + limit);
        }
        
        if (start != null) {
            joiner.add("start=" + start);
        } else if (since != null) {
            joiner.add("since=" + since);
        }
        
        if (end != null) {
            joiner.add("end=" + end);
        }
        
        if (direction != null) {
            joiner.add("direction=" + direction);
        }
        
        return joiner.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LokiRangeQuery that = (LokiRangeQuery) o;
        return Objects.equals(query, that.query) &&
                Objects.equals(limit, that.limit) &&
                Objects.equals(start, that.start) &&
                Objects.equals(end, that.end) &&
                Objects.equals(since, that.since) &&
                Objects.equals(direction, that.direction);
    }

    @Override
    public int hashCode() {
        return Objects.hash(query, limit, start, end, since, direction);
    }

    @Override
    public String toString() {
        return "LokiRangeQuery{" +
                "query='" + query + '\'' +
                ", limit=" + limit +
                ", start=" + start +
                ", end=" + end +
                ", since='" + since + '\'' +
                ", direction='" + direction + '\'' +
                '}';
    }
}
