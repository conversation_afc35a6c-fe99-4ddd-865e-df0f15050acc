package com.innodealing.onshore.monitor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.model.MonitorInfo;
import com.innodealing.onshore.monitor.model.TriggerCode;
import com.innodealing.onshore.monitor.service.MonitorMetaService;
import com.innodealing.onshore.monitor.service.MonitorService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MonitorApplication.class)
public class MonitorMorningReportTests {

    @Resource
    private MonitorService monitorService;
    @Resource
    private MonitorMetaService monitorMetaService;

    @Test
    public void testGenerateReport() throws JsonProcessingException {
        List<MonitorInfo> monitorInfos = monitorMetaService.listAllMonitorInfos();
        List<MonitorExecuteInfo> success = new ArrayList<>();
        List<MonitorExecuteInfo> fail = new ArrayList<>();
        List<MonitorExecuteInfo> skywalkings = new ArrayList<>();
        for (MonitorInfo monitorInfo : monitorInfos) {
            MonitorExecuteInfo executeInfo = monitorService.execute(monitorInfo.getMonitorName());
            Integer triggerCode = executeInfo.getTriggerCode();
            Integer handleCode = executeInfo.getHandleCode();
            if (Objects.equals(monitorInfo.getMonitorName(), "skyWalkingMonitor")) {
                skywalkings.add(executeInfo);
            } else if (!Objects.equals(triggerCode, TriggerCode.SUCCESS) || !Objects.equals(handleCode, HandleCode.SUCCESS)) {
                fail.add(executeInfo);
            } else {
                success.add(executeInfo);
            }
        }

        MorningReport morningReport = new MorningReport(success, fail, skywalkings);
        System.out.println(morningReport.simpleReport());
    }

    class MorningReport {

        private final List<MonitorExecuteInfo> success;
        private final List<MonitorExecuteInfo> fail;
        private final List<MonitorExecuteInfo> skywalkings;

        public MorningReport(List<MonitorExecuteInfo> success, List<MonitorExecuteInfo> fail, List<MonitorExecuteInfo> skywalkings) {
            this.success = success;
            this.fail = fail;
            this.skywalkings = skywalkings;
        }

        public String simpleReport() throws JsonProcessingException {
            Map<String, List<Object>> mapMap = new TreeMap<>();
            List<Object> successDescs = success.stream().map(MonitorExecuteInfo::getDesc).collect(Collectors.toList());
            List<Object> failDescs = fail.stream().map(MonitorExecuteInfo::getDesc).collect(Collectors.toList());
            mapMap.put("巡检正常:", successDescs);
            mapMap.put("巡检失败:", failDescs);
            mapMap.put("skywalking:", skywalkings.stream().map(MonitorExecuteInfo::getMonitorReport).collect(Collectors.toList()));
            return new ObjectMapper().writeValueAsString(mapMap);
        }
    }
}
