package com.datafeed.monitor.service.impl;

import com.datafeed.monitor.model.DatafeedDashboardData;
import com.datafeed.monitor.model.DatafeedQueryRequest;
import com.datafeed.monitor.repository.DatafeedOriginMessageRepository;
import com.datafeed.monitor.repository.DatafeedUserTemplateConfigRepository;
import com.datafeed.monitor.service.DatafeedMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Datafeed监控服务实现
 */
@Slf4j
@Service
public class DatafeedMonitorServiceImpl implements DatafeedMonitorService {
    
    @Autowired
    private DatafeedOriginMessageRepository messageRepository;
    
    @Autowired
    private DatafeedUserTemplateConfigRepository userTemplateConfigRepository;
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Override
    public DatafeedDashboardData getDashboardData() {
        LocalDateTime today = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime tomorrow = today.plusDays(1);
        return getDashboardData(today, tomorrow);
    }
    
    @Override
    public DatafeedDashboardData getDashboardData(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 获取在线用户
            List<String> onlineUsers = getOnlineUsers();
            
            // 获取当日发送数量
            Long todaySendCount = messageRepository.countByCreateTimeBetween(startTime, endTime);
            
            // 获取发送失败数量（这里暂时返回0，实际需要从Loki查询）
            Long failureCount = getFailureCount(startTime, endTime);
            
            // 计算成功率
            Double successRate = todaySendCount > 0 ? 
                ((double)(todaySendCount - failureCount) / todaySendCount) * 100 : 0.0;
            
            // 获取趋势数据
            DatafeedQueryRequest trendRequest = DatafeedQueryRequest.builder()
                    .timeUnit("minute")
                    .startTime(startTime)
                    .endTime(endTime)
                    .build();
            List<DatafeedDashboardData.TrendDataPoint> trendData = getSendTrend(trendRequest);
            
            // 获取产品统计
            List<DatafeedDashboardData.ProductStatistics> productStatistics = getProductStatistics(startTime, endTime);
            
            return DatafeedDashboardData.builder()
                    .onlineUserCount(onlineUsers.size())
                    .onlineUsers(onlineUsers)
                    .todaySendCount(todaySendCount)
                    .failureCount(failureCount)
                    .successRate(successRate)
                    .trendData(trendData)
                    .productStatistics(productStatistics)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取仪表板数据失败", e);
            return DatafeedDashboardData.builder()
                    .onlineUserCount(0)
                    .onlineUsers(new ArrayList<>())
                    .todaySendCount(0L)
                    .failureCount(0L)
                    .successRate(0.0)
                    .trendData(new ArrayList<>())
                    .productStatistics(new ArrayList<>())
                    .build();
        }
    }
    
    @Override
    public List<DatafeedDashboardData.TrendDataPoint> getSendTrend(DatafeedQueryRequest request) {
        try {
            List<Object[]> results;
            if ("minute".equals(request.getTimeUnit())) {
                results = messageRepository.countByMinuteGrouping(request.getStartTime(), request.getEndTime());
            } else {
                results = messageRepository.countBySecondGrouping(request.getStartTime(), request.getEndTime());
            }
            
            return results.stream()
                    .map(result -> DatafeedDashboardData.TrendDataPoint.builder()
                            .timePoint((String) result[1])
                            .count(((Number) result[0]).longValue())
                            .build())
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取发送趋势数据失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<DatafeedDashboardData.ProductStatistics> getProductStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            List<Object[]> results = messageRepository.countByProductCodeGrouping(startTime, endTime);
            
            // 计算总数用于计算百分比
            long totalCount = results.stream()
                    .mapToLong(result -> ((Number) result[1]).longValue())
                    .sum();
            
            return results.stream()
                    .map(result -> {
                        String productCode = (String) result[0];
                        Long count = ((Number) result[1]).longValue();
                        Double percentage = totalCount > 0 ? (double) count / totalCount * 100 : 0.0;
                        
                        return DatafeedDashboardData.ProductStatistics.builder()
                                .productCode(productCode)
                                .count(count)
                                .percentage(percentage)
                                .build();
                    })
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取产品统计数据失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<String> getOnlineUsers() {
        try {
            // 调用QuickFIX服务获取在线用户
            // curl -X GET "http://localhost:9303/dm-quickfixj-server/internal/session/list"
            String url = "http://localhost:9303/dm-quickfixj-server/internal/session/list";
            
            // 这里需要根据实际API响应格式解析
            // 暂时返回空列表，实际实现需要解析JSON响应
            return new ArrayList<>();
            
        } catch (Exception e) {
            log.error("获取在线用户失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<String> getAllUsers() {
        try {
            return userTemplateConfigRepository.findDistinctUserAccounts();
        } catch (Exception e) {
            log.error("获取所有用户失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public Long getTodaySendCount() {
        LocalDateTime today = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime tomorrow = today.plusDays(1);
        return messageRepository.countByCreateTimeBetween(today, tomorrow);
    }
    
    @Override
    public Long getFailureCount(LocalDateTime startTime, LocalDateTime endTime) {
        // 这里需要从Loki查询失败数据
        // 查询条件: {app="onshore-datafeed-processing"} |= `error messageId:`
        // 暂时返回0
        return 0L;
    }
}
