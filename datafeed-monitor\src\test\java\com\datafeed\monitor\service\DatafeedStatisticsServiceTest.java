package com.datafeed.monitor.service;

import com.datafeed.monitor.model.DatafeedStatistics;
import com.datafeed.monitor.model.StatisticsRequest;
import com.datafeed.monitor.model.StatisticsResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据发送统计服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class DatafeedStatisticsServiceTest {

    @Test
    public void testDatafeedStatisticsModel() {
        // 测试数据模型创建
        DatafeedStatistics statistics = DatafeedStatistics.builder()
                .timestamp(new Timestamp(System.currentTimeMillis()))
                .timeUnit("minute")
                .sendCount(100L)
                .dataVolume(1024L)
                .successCount(95L)
                .failureCount(5L)
                .avgResponseTime(150.0)
                .dataSource("test-source")
                .targetSystem("test-target")
                .build();
        
        assertNotNull(statistics);
        assertEquals("minute", statistics.getTimeUnit());
        assertEquals(100L, statistics.getSendCount());
        assertEquals(1024L, statistics.getDataVolume());
        assertEquals(95L, statistics.getSuccessCount());
        assertEquals(5L, statistics.getFailureCount());
        assertEquals(150.0, statistics.getAvgResponseTime());
        assertEquals("test-source", statistics.getDataSource());
        assertEquals("test-target", statistics.getTargetSystem());
    }

    @Test
    public void testStatisticsRequest() {
        // 测试请求模型创建
        StatisticsRequest request = StatisticsRequest.builder()
                .timeUnit("minute")
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .dataSource("test-source")
                .targetSystem("test-target")
                .build();
        
        assertNotNull(request);
        assertEquals("minute", request.getTimeUnit());
        assertNotNull(request.getStartTime());
        assertNotNull(request.getEndTime());
        assertEquals("test-source", request.getDataSource());
        assertEquals("test-target", request.getTargetSystem());
    }

    @Test
    public void testStatisticsResponse() {
        // 测试响应模型创建
        StatisticsResponse response = StatisticsResponse.builder()
                .totalSendCount(1000L)
                .totalDataVolume(10240L)
                .totalSuccessCount(950L)
                .totalFailureCount(50L)
                .avgSendRate(16.67)
                .peakSendRate(100L)
                .successRate(95.0)
                .avgResponseTime(150.0)
                .timeUnit("minute")
                .build();
        
        assertNotNull(response);
        assertEquals(1000L, response.getTotalSendCount());
        assertEquals(10240L, response.getTotalDataVolume());
        assertEquals(950L, response.getTotalSuccessCount());
        assertEquals(50L, response.getTotalFailureCount());
        assertEquals(16.67, response.getAvgSendRate());
        assertEquals(100L, response.getPeakSendRate());
        assertEquals(95.0, response.getSuccessRate());
        assertEquals(150.0, response.getAvgResponseTime());
        assertEquals("minute", response.getTimeUnit());
    }
}
