package com.datafeed.monitor.repository;

import com.datafeed.monitor.entity.DatafeedUserTemplateConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户模板配置Repository
 */
@Repository
public interface DatafeedUserTemplateConfigRepository extends JpaRepository<DatafeedUserTemplateConfig, Long> {
    
    /**
     * 获取所有不同的用户账号
     */
    @Query("SELECT DISTINCT u.userAccount FROM DatafeedUserTemplateConfig u")
    List<String> findDistinctUserAccounts();
    
    /**
     * 根据用户账号查找配置
     */
    List<DatafeedUserTemplateConfig> findByUserAccount(String userAccount);
    
    /**
     * 根据模板ID查找配置
     */
    List<DatafeedUserTemplateConfig> findByTemplateId(Long templateId);
}
