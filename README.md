# Monitor Dashboard

DM在线监控平台 - 企业级监控解决方案

## 📋 项目简介

本项目源自monitor服务，由于monitor的功能不断升级，代码和承担的任务越来越多，因此将项目进行了模块化拆分，形成了完整的监控平台解决方案。

## 🏗️ 项目架构

```
monitor-dashboard/
├── loki-sdk/              # Loki日志查询SDK
├── skywalking-sdk/        # SkyWalking监控SDK
├── monitor/               # 监控核心服务
├── monitor-react/         # React前端界面
└── .gitignore            # Git忽略规则
```

## 📦 模块说明

### 🔍 loki-sdk
**Loki日志查询和监控SDK**
- **技术栈**: Java 8, Spring Boot 2.4.2, OkHttp, Gson
- **功能**:
  - Loki日志查询API封装
  - 日志范围查询支持
  - 错误日志监控
  - 自动配置支持

### 📊 skywalking-sdk
**SkyWalking监控和错误日志处理SDK**
- **技术栈**: Java 8, Spring Boot 2.4.2, <PERSON>, Unirest
- **功能**:
  - SkyWalking错误日志处理
  - 慢查询URL检查
  - 钉钉消息推送
  - 监控数据处理

### 🖥️ monitor
**监控核心服务**
- **技术栈**: Java 8, Spring Boot 2.4.2, MySQL, Thymeleaf
- **端口**: 8081
- **主要功能**:
  - **监控模块**:
    - 券商客户端监控 (BGC, CNEX, 平安, 天津信托, TPS等)
    - 券商报价监控 (CFETS报价流、FlexScreen等)
    - 资金端监控 (综合屏CFETS、交易所监控)
    - 期货合约监控 (主力合约、今日合约、基差合约)
    - 外汇客户端监控
    - 数据源监控 (华夏客户端)
    - 其他监控 (CFETS价格、连接跟踪、XXL-JOB等)
  - **告警系统**:
    - 邮件告警
    - 飞书告警
    - 电话告警
    - 短信告警
    - 日志告警
  - **SkyWalking集成**:
    - 错误日志监控
    - 性能监控
    - 服务监控
  - **API接口**:
    - 监控数据查询
    - 用户排班管理
    - 交接功能
    - Loki测试接口

### 🎨 monitor-react
**React前端监控界面**
- **技术栈**: React 18, Vite 5, Ant Design 5, Axios
- **端口**: 3001 (开发环境)
- **功能**:
  - 监控仪表板
  - 实时监控数据展示
  - 巡检面板
  - 用户排班管理
  - 交接操作界面

## 🚀 快速开始

### 环境要求
- Java 8+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 1. 克隆项目
```bash
git clone <repository-url>
cd monitor-dashboard
```

### 2. 构建SDK模块
```bash
# 构建loki-sdk
cd loki-sdk
mvn clean install

# 构建skywalking-sdk
cd ../skywalking-sdk
mvn clean install
```

### 3. 启动后端服务
```bash
cd ../monitor
mvn spring-boot:run
```
服务将在 http://localhost:8081 启动

### 4. 启动前端服务
```bash
cd ../monitor-react
npm install
npm run dev
```
前端将在 http://localhost:3001 启动

## 🔧 配置说明

### 后端配置
主要配置文件位于 `monitor/src/main/resources/`:
- `application.yaml` - 主配置文件
- `application-datasource.yaml` - 数据源配置
- `application-sdk-loki.yaml` - Loki SDK配置
- `application-sdk-skywalking.yaml` - SkyWalking SDK配置

### 前端配置
- `monitor-react/vite.config.js` - Vite配置，包含代理设置
- `monitor-react/package.json` - 依赖和脚本配置

### 代理配置
前端开发环境通过Vite代理访问后端：
```javascript
proxy: {
  '/monitor': {
    target: 'http://localhost:8081',
    changeOrigin: true
  }
}
```

## 📚 API文档

### 主要接口
- `GET /monitor/api/log` - 获取监控日志
- `GET /monitor/api/today-user` - 获取当前巡检信息
- `GET /monitor/api/next-user` - 获取下个工作日巡检信息
- `POST /monitor/api/handover` - 执行交接操作
- `POST /monitor/api/update-next-schedule` - 更新排班

### Swagger文档
启动后端服务后访问: http://localhost:8081/swagger-ui.html

## 🗄️ 数据库

项目使用MySQL数据库存储监控数据和用户排班信息。

## 📝 开发指南

### 添加新的监控模块
1. 在 `monitor/src/main/java/com/innodealing/onshore/monitor/monitor/` 下创建新的监控类
2. 继承相应的基础监控类
3. 实现监控逻辑
4. 在配置中注册新的监控任务

### 添加新的告警方式
1. 在 `monitor/src/main/java/com/innodealing/onshore/monitor/alter/` 下创建告警消费者
2. 实现 `AlterConsumer` 接口
3. 配置告警规则和触发条件

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用私有许可证，仅供内部使用。

## 📞 联系方式

如有问题或建议，请联系开发团队。