@echo off
echo ========================================
echo   DataFeed Monitor - 启动所有服务
echo ========================================
echo.

echo 正在启动后端服务...
start "DataFeed Backend" cmd /k "cd /d W:\gitRep\monitor-dashboard\datafeed-monitor && mvn spring-boot:run"

echo 等待后端服务启动...
timeout /t 10 /nobreak > nul

echo 正在启动前端应用...
start "DataFeed Frontend" cmd /k "cd /d W:\gitRep\monitor-dashboard\datafeed-react && npm start"

echo 等待前端应用启动...
timeout /t 5 /nobreak > nul

echo.
echo ========================================
echo   服务启动完成！
echo ========================================
echo.
echo 后端服务: http://localhost:8082/datafeed-monitor
echo 前端应用: http://localhost:3000
echo 状态检查: file:///W:/gitRep/monitor-dashboard/service-status.html
echo.

echo 正在打开服务状态页面...
start "" "file:///W:/gitRep/monitor-dashboard/service-status.html"

echo.
echo 按任意键退出...
pause > nul
