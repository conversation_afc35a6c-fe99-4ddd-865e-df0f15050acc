import { useState, useEffect, useCallback } from 'react';
import DatafeedService, { DashboardData, TrendDataPoint, ProductStatistics, OnlineUsersResponse, TodayStatistics } from '../services/datafeedService';

export interface UseDatafeedDataReturn {
  dashboardData: DashboardData | null;
  trendData: TrendDataPoint[];
  productStats: ProductStatistics[];
  onlineUsers: OnlineUsersResponse | null;
  todayStats: TodayStatistics | null;
  loading: boolean;
  error: string | null;
  refreshData: () => void;
  refreshTrendData: (timeUnit?: 'minute' | 'second', minutes?: number) => void;
}

export const useDatafeedData = (autoRefresh: boolean = true, refreshInterval: number = 30000): UseDatafeedDataReturn => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [trendData, setTrendData] = useState<TrendDataPoint[]>([]);
  const [productStats, setProductStats] = useState<ProductStatistics[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<OnlineUsersResponse | null>(null);
  const [todayStats, setTodayStats] = useState<TodayStatistics | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // 获取仪表板数据
  const fetchDashboardData = useCallback(async () => {
    try {
      const data = await DatafeedService.getDashboardData();
      setDashboardData(data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setError('获取仪表板数据失败');
    }
  }, []);

  // 获取趋势数据
  const fetchTrendData = useCallback(async (timeUnit: 'minute' | 'second' = 'minute', minutes: number = 60) => {
    try {
      const data = await DatafeedService.getRealtimeTrend(timeUnit, minutes);
      setTrendData(data);
    } catch (err) {
      console.error('Failed to fetch trend data:', err);
    }
  }, []);

  // 获取产品统计数据
  const fetchProductStats = useCallback(async () => {
    try {
      const data = await DatafeedService.getProductStatistics();
      setProductStats(data);
    } catch (err) {
      console.error('Failed to fetch product statistics:', err);
    }
  }, []);

  // 获取在线用户数据
  const fetchOnlineUsers = useCallback(async () => {
    try {
      const data = await DatafeedService.getOnlineUsers();
      setOnlineUsers(data);
    } catch (err) {
      console.error('Failed to fetch online users:', err);
    }
  }, []);

  // 获取今日统计数据
  const fetchTodayStats = useCallback(async () => {
    try {
      const data = await DatafeedService.getTodayStatistics();
      setTodayStats(data);
    } catch (err) {
      console.error('Failed to fetch today statistics:', err);
    }
  }, []);

  // 刷新所有数据
  const refreshData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchDashboardData(),
        fetchTrendData(),
        fetchProductStats(),
        fetchOnlineUsers(),
        fetchTodayStats()
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchDashboardData, fetchTrendData, fetchProductStats, fetchOnlineUsers, fetchTodayStats]);

  // 刷新趋势数据
  const refreshTrendData = useCallback((timeUnit: 'minute' | 'second' = 'minute', minutes: number = 60) => {
    fetchTrendData(timeUnit, minutes);
  }, [fetchTrendData]);

  // 初始化数据加载
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshData]);

  return {
    dashboardData,
    trendData,
    productStats,
    onlineUsers,
    todayStats,
    loading,
    error,
    refreshData,
    refreshTrendData
  };
};
