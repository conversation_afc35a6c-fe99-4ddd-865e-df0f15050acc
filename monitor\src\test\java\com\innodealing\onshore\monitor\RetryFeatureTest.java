package com.innodealing.onshore.monitor;

import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorClient;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.model.MonitorReport;
import com.innodealing.onshore.monitor.report.DefaultReport;
import com.innodealing.onshore.monitor.service.MonitorService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 重试功能集成测试
 */
@SpringBootTest
@ActiveProfiles("local")
class RetryFeatureTest {

    @Resource
    private MonitorService monitorService;

    /**
     * 测试用的重试监控器
     */
    @MonitorClient(desc = "重试功能测试监控器", enable = true)
    public static class TestRetryMonitor implements Monitor {
        private static final AtomicInteger callCount = new AtomicInteger(0);

        @Override
        public boolean monitor() {
            return reportMonitor().isAccess();
        }

        @Override
        public MonitorReport reportMonitor() {
            int count = callCount.incrementAndGet();
            System.out.println("TestRetryMonitor 被调用，第 " + count + " 次");

            // 前两次调用模拟失败，第三次成功
            if (count <= 2) {
                System.out.println("TestRetryMonitor 第 " + count + " 次调用模拟失败");
                throw new RuntimeException("模拟监控失败，用于演示重试机制 - 第" + count + "次调用");
            }

            System.out.println("TestRetryMonitor 第 " + count + " 次调用成功");
            return new MonitorReport(true, DefaultReport.success());
        }

        public static void resetCallCount() {
            callCount.set(0);
        }

        public static int getCallCount() {
            return callCount.get();
        }
    }

    @Test
    void testRetryFeatureIntegration() {
        // 重置计数器
        TestRetryMonitor.resetCallCount();

        System.out.println("=== 开始测试重试功能 ===");

        // 执行监控，前两次会失败，第三次成功
        MonitorExecuteInfo result = monitorService.execute(TestRetryMonitor.class);
        
        // 验证基本信息
        assertNotNull(result, "执行结果不应为空");
        assertNotNull(result.getJobId(), "任务ID不应为空");
        assertEquals("重试功能演示监控器", result.getDesc(), "任务描述应该匹配");
        
        // 验证重试信息
        assertEquals(2, result.getRetryCount().intValue(), "应该重试了2次");
        assertEquals(2, result.getMaxRetryCount().intValue(), "最大重试次数应该是2");
        
        // 验证最终状态
        assertEquals(HandleCode.SUCCESS, result.getHandleCode().intValue(), "最终状态应该是成功");
        assertNotNull(result.getMonitorReport(), "监控报告不应为空");
        assertTrue(result.getMonitorReport().isAccess(), "监控应该最终成功");
        
        // 验证重试历史
        assertNotNull(result.getRetryHistory(), "重试历史不应为空");
        assertEquals(2, result.getRetryHistory().size(), "应该有2条重试记录");
        
        // 验证重试记录详情
        for (int i = 0; i < result.getRetryHistory().size(); i++) {
            MonitorExecuteInfo.RetryRecord record = result.getRetryHistory().get(i);
            assertEquals(i + 1, record.getRetryNumber().intValue(), "重试序号应该正确");
            assertNotNull(record.getRetryTime(), "重试时间不应为空");
            assertEquals("Task timeout, attempting retry", record.getReason(), "重试原因应该正确");
        }
        
        // 验证总调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, TestRetryMonitor.getCallCount(), "总调用次数应该是3次");

        // 输出测试结果
        System.out.println("任务ID: " + result.getJobId());
        System.out.println("任务描述: " + result.getDesc());
        System.out.println("最终状态: " + result.getHandleCode());
        System.out.println("重试次数: " + result.getRetryCount());
        System.out.println("最大重试次数: " + result.getMaxRetryCount());
        System.out.println("重试历史记录数: " + result.getRetryHistory().size());
        System.out.println("总调用次数: " + TestRetryMonitor.getCallCount());
        System.out.println("监控最终结果: " + (result.getMonitorReport().isAccess() ? "成功" : "失败"));
        
        // 输出重试历史详情
        System.out.println("\n=== 重试历史详情 ===");
        for (int i = 0; i < result.getRetryHistory().size(); i++) {
            MonitorExecuteInfo.RetryRecord record = result.getRetryHistory().get(i);
            System.out.println(String.format("重试 %d: 时间=%s, 原因=%s, 结果=%s", 
                record.getRetryNumber(), 
                record.getRetryTime(), 
                record.getReason(),
                record.getResultCode() != null ? record.getResultCode() : "未设置"
            ));
        }
        
        System.out.println("=== 重试功能测试完成 ===");
    }
    
    /**
     * 测试重试功能的配置
     */
    @Test
    void testRetryConfiguration() {
        System.out.println("=== 测试重试配置 ===");
        
        // 创建一个新的执行信息来验证默认配置
        MonitorExecuteInfo executeInfo = new MonitorExecuteInfo();
        assertEquals(0, executeInfo.getRetryCount().intValue(), "初始重试次数应该是0");
        assertEquals(2, executeInfo.getMaxRetryCount().intValue(), "默认最大重试次数应该是2");
        assertNotNull(executeInfo.getRetryHistory(), "重试历史应该被初始化");
        assertTrue(executeInfo.getRetryHistory().isEmpty(), "初始重试历史应该为空");
        
        System.out.println("默认重试配置验证通过");
    }
}
