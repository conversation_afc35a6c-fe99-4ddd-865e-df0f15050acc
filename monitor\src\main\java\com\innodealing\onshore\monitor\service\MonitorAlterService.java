package com.innodealing.onshore.monitor.service;

import com.innodealing.onshore.monitor.alter.AlterConsumer;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;

import java.util.List;
import lombok.Getter;

/**
 * 监控告警服务接口
 * 负责管理监控告警的队列、处理和发送
 */
public interface MonitorAlterService {

    /**
     * 添加告警到队列
     * 
     * @param alterConsumer 告警消费者
     */
    void addAlarm(AlterConsumer alterConsumer);

    /**
     * 根据监控执行信息和告警消费者类型创建并添加告警
     * 
     * @param executeInfo 监控执行信息
     * @param alterConsumerClass 告警消费者类型
     */
    void addAlarm(MonitorExecuteInfo executeInfo, Class<? extends AlterConsumer> alterConsumerClass);

    /**
     * 处理告警队列中的所有告警
     * 该方法会被定时调用来处理待发送的告警
     */
    void processAlarms();

    /**
     * 获取当前告警队列的大小
     * 
     * @return 告警队列中待处理的告警数量
     */
    int getAlarmQueueSize();

    /**
     * 获取告警队列中的所有告警（用于监控和调试）
     * 注意：此方法会清空队列
     * 
     * @return 当前队列中的所有告警消费者列表
     */
    List<AlterConsumer> drainAlarmQueue();

    /**
     * 清空告警队列
     */
    void clearAlarmQueue();

    /**
     * 检查告警队列是否为空
     * 
     * @return 如果队列为空返回true，否则返回false
     */
    boolean isAlarmQueueEmpty();

    /**
     * 获取告警处理统计信息
     * 
     * @return 告警处理统计信息
     */
    AlarmStatistics getAlarmStatistics();

    /**
     * 告警统计信息
     */
    @Getter
    class AlarmStatistics {
        private final long totalProcessed;
        private final long totalSuccess;
        private final long totalFailed;
        private final long lastProcessTime;

        public AlarmStatistics(long totalProcessed, long totalSuccess, long totalFailed, long lastProcessTime) {
            this.totalProcessed = totalProcessed;
            this.totalSuccess = totalSuccess;
            this.totalFailed = totalFailed;
            this.lastProcessTime = lastProcessTime;
        }

        @Override
        public String toString() {
            return String.format("AlarmStatistics{totalProcessed=%d, totalSuccess=%d, totalFailed=%d, lastProcessTime=%d}",
                    totalProcessed, totalSuccess, totalFailed, lastProcessTime);
        }
    }
}
