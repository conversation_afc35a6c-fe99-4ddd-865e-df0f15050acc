package com.innodealing.onshore.monitor;

import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorClient;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.model.MonitorReport;
import com.innodealing.onshore.monitor.report.DefaultReport;
import com.innodealing.onshore.monitor.service.MonitorService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 异步执行重试功能测试
 */
@SpringBootTest
@ActiveProfiles("local")
class AsyncRetryTest {

    @Resource
    private MonitorService monitorService;

    /**
     * 异步执行重试测试监控器
     */
    @MonitorClient(desc = "异步重试测试监控器", enable = true)
    public static class AsyncRetryTestMonitor implements Monitor {
        private static final AtomicInteger callCount = new AtomicInteger(0);
        
        @Override
        public boolean monitor() {
            return reportMonitor().isAccess();
        }

        @Override
        public MonitorReport reportMonitor() {
            int count = callCount.incrementAndGet();
            System.out.println("AsyncRetryTestMonitor 被调用，第 " + count + " 次");
            
            // 前两次调用模拟失败，第三次成功
            if (count <= 2) {
                System.out.println("AsyncRetryTestMonitor 第 " + count + " 次调用模拟失败");
                throw new RuntimeException("模拟监控失败，用于演示异步重试机制 - 第" + count + "次调用");
            }
            
            System.out.println("AsyncRetryTestMonitor 第 " + count + " 次调用成功");
            return new MonitorReport(true, DefaultReport.success());
        }
        
        public static void resetCallCount() {
            callCount.set(0);
        }
        
        public static int getCallCount() {
            return callCount.get();
        }
    }

    /**
     * 异步执行始终失败的测试监控器
     */
    @MonitorClient(desc = "异步失败测试监控器", enable = true)
    public static class AsyncFailTestMonitor implements Monitor {
        private static final AtomicInteger callCount = new AtomicInteger(0);
        
        @Override
        public boolean monitor() {
            return reportMonitor().isAccess();
        }

        @Override
        public MonitorReport reportMonitor() {
            int count = callCount.incrementAndGet();
            System.out.println("AsyncFailTestMonitor 被调用，第 " + count + " 次");
            
            // 始终失败
            throw new RuntimeException("模拟监控始终失败，用于演示异步重试机制 - 第" + count + "次调用");
        }
        
        public static void resetCallCount() {
            callCount.set(0);
        }
        
        public static int getCallCount() {
            return callCount.get();
        }
    }

    @Test
    void testAsyncRetrySuccess() throws InterruptedException {
        AsyncRetryTestMonitor.resetCallCount();
        
        System.out.println("=== 开始测试异步执行重试功能（成功场景） ===");
        
        // 异步执行监控，前两次会失败，第三次成功
        MonitorExecuteInfo result = monitorService.execute("AsyncRetryTestMonitor", true, false);
        
        // 异步执行会立即返回，此时状态应该是RUNNING
        assertNotNull(result, "执行结果不应为空");
        assertEquals(HandleCode.RUNNING, result.getHandleCode().intValue(), "异步执行初始状态应该是RUNNING");
        
        // 等待异步任务完成（包括重试）
        // 预计时间：初始执行 + 重试1(30秒延迟) + 重试2(30秒延迟) = 约60秒+执行时间
        System.out.println("等待异步任务完成（包括重试过程）...");
        Thread.sleep(70000); // 等待70秒确保重试完成
        
        // 重新获取任务信息
        MonitorExecuteInfo finalResult = monitorService.getJobInfo(result.getJobId());
        
        // 验证最终结果
        assertNotNull(finalResult, "最终结果不应为空");
        assertEquals(HandleCode.SUCCESS, finalResult.getHandleCode().intValue(), "最终状态应该是成功");
        assertEquals(2, finalResult.getRetryCount().intValue(), "应该重试了2次");
        assertEquals(2, finalResult.getMaxRetryCount().intValue(), "最大重试次数应该是2");
        
        // 验证重试历史
        assertNotNull(finalResult.getRetryHistory(), "重试历史不应为空");
        assertEquals(2, finalResult.getRetryHistory().size(), "应该有2条重试记录");
        
        // 验证总调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, AsyncRetryTestMonitor.getCallCount(), "总调用次数应该是3次");
        
        // 验证最终成功
        assertNotNull(finalResult.getMonitorReport(), "监控报告不应为空");
        assertTrue(finalResult.getMonitorReport().isAccess(), "监控应该最终成功");
        
        System.out.println("异步重试成功测试完成");
        System.out.println("最终状态: " + finalResult.getHandleCode());
        System.out.println("重试次数: " + finalResult.getRetryCount());
        System.out.println("总调用次数: " + AsyncRetryTestMonitor.getCallCount());
    }

    @Test
    void testAsyncRetryFailure() throws InterruptedException {
        AsyncFailTestMonitor.resetCallCount();
        
        System.out.println("=== 开始测试异步执行重试功能（失败场景） ===");
        
        // 异步执行监控，始终失败
        MonitorExecuteInfo result = monitorService.execute("AsyncFailTestMonitor", true, false);
        
        // 异步执行会立即返回，此时状态应该是RUNNING
        assertNotNull(result, "执行结果不应为空");
        assertEquals(HandleCode.RUNNING, result.getHandleCode().intValue(), "异步执行初始状态应该是RUNNING");
        
        // 等待异步任务完成（包括重试）
        System.out.println("等待异步任务完成（包括重试过程）...");
        Thread.sleep(70000); // 等待70秒确保重试完成
        
        // 重新获取任务信息
        MonitorExecuteInfo finalResult = monitorService.getJobInfo(result.getJobId());
        
        // 验证最终结果
        assertNotNull(finalResult, "最终结果不应为空");
        assertEquals(HandleCode.EXCEPTION, finalResult.getHandleCode().intValue(), "最终状态应该是异常");
        assertEquals(2, finalResult.getRetryCount().intValue(), "应该重试了2次");
        assertEquals(2, finalResult.getMaxRetryCount().intValue(), "最大重试次数应该是2");
        
        // 验证重试历史
        assertNotNull(finalResult.getRetryHistory(), "重试历史不应为空");
        assertEquals(2, finalResult.getRetryHistory().size(), "应该有2条重试记录");
        
        // 验证总调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, AsyncFailTestMonitor.getCallCount(), "总调用次数应该是3次");
        
        System.out.println("异步重试失败测试完成");
        System.out.println("最终状态: " + finalResult.getHandleCode());
        System.out.println("重试次数: " + finalResult.getRetryCount());
        System.out.println("总调用次数: " + AsyncFailTestMonitor.getCallCount());
    }

    @Test
    void testSyncRetryStillWorks() {
        AsyncRetryTestMonitor.resetCallCount();
        
        System.out.println("=== 验证同步执行重试功能仍然正常 ===");
        
        // 同步执行监控，前两次会失败，第三次成功
        MonitorExecuteInfo result = monitorService.execute("AsyncRetryTestMonitor", false, false);
        
        // 验证结果
        assertNotNull(result, "执行结果不应为空");
        assertEquals(HandleCode.SUCCESS, result.getHandleCode().intValue(), "最终状态应该是成功");
        assertEquals(2, result.getRetryCount().intValue(), "应该重试了2次");
        assertEquals(2, result.getMaxRetryCount().intValue(), "最大重试次数应该是2");
        
        // 验证重试历史
        assertNotNull(result.getRetryHistory(), "重试历史不应为空");
        assertEquals(2, result.getRetryHistory().size(), "应该有2条重试记录");
        
        // 验证总调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, AsyncRetryTestMonitor.getCallCount(), "总调用次数应该是3次");
        
        // 验证最终成功
        assertNotNull(result.getMonitorReport(), "监控报告不应为空");
        assertTrue(result.getMonitorReport().isAccess(), "监控应该最终成功");
        
        System.out.println("同步重试功能验证完成");
        System.out.println("最终状态: " + result.getHandleCode());
        System.out.println("重试次数: " + result.getRetryCount());
        System.out.println("总调用次数: " + AsyncRetryTestMonitor.getCallCount());
    }
}
