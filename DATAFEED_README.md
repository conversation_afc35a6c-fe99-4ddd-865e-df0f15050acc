# DataFeed Monitor Dashboard

基于SQL结构的DataFeed监控系统，包含完整的后端API和前端界面，支持数据发送趋势统计、时间单位选择、时间范围查询等核心功能。

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）

1. **启动所有服务**
   ```bash
   双击运行: start-all.bat
   ```

2. **分别启动服务**
   ```bash
   后端服务: start-backend.bat
   前端应用: start-frontend.bat
   ```

### 方式二：手动启动

1. **启动后端服务**
   ```bash
   cd datafeed-monitor
   mvn spring-boot:run
   ```

2. **启动前端应用**
   ```bash
   cd datafeed-react
   npm start
   ```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端应用** | http://localhost:3000 | 主要监控界面 |
| **后端API** | http://localhost:8082/datafeed-monitor | RESTful API服务 |
| **服务状态** | [service-status.html](file:///W:/gitRep/monitor-dashboard/service-status.html) | 服务状态检查页面 |
| **API测试** | [test-apis.html](file:///W:/gitRep/monitor-dashboard/test-apis.html) | API接口测试页面 |

## 📊 主要功能

### 仪表板 (Dashboard)
- ✅ 实时监控数据
- ✅ 关键指标展示（总账号数、在线账号、今日发送量、成功率）
- ✅ 数据发送趋势图
- ✅ 产品分布饼图
- ✅ 在线账号列表
- ✅ 自动刷新（30秒间隔）

### 数据统计 (DataStatistics)
- ✅ 时间单位选择（按分钟/按秒）
- ✅ 时间范围选择
- ✅ 统计概览（总发送条数、总数据量、平均速率、峰值速率）
- ✅ 交互式趋势图表
- ✅ 数据导出功能（CSV格式）

## 🔧 技术栈

### 后端
- **框架**: Spring Boot 2.4.2
- **语言**: Java 8
- **构建工具**: Maven
- **数据**: 模拟数据服务

### 前端
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design
- **图表库**: ECharts
- **HTTP客户端**: Axios
- **构建工具**: Create React App

## 📡 API 接口

### 核心接口
- `GET /api/datafeed/health` - 健康检查
- `GET /api/datafeed/dashboard` - 仪表板数据
- `GET /api/datafeed/users/online` - 在线用户
- `GET /api/datafeed/statistics/today` - 今日统计
- `GET /api/datafeed/trend/realtime` - 实时趋势
- `POST /api/datafeed/trend` - 发送趋势查询
- `GET /api/datafeed/products/statistics` - 产品统计

### 查询参数
- `timeUnit`: 时间单位（minute/second）
- `startTime`: 开始时间
- `endTime`: 结束时间
- `minutes`: 时间范围（分钟）

## 🛠️ 开发环境要求

### 后端
- Java 8+
- Maven 3.6+
- 端口 8082 可用

### 前端
- Node.js 14+
- npm 6+
- 端口 3000 可用

## 📁 项目结构

```
monitor-dashboard/
├── datafeed-monitor/           # 后端Spring Boot项目
│   ├── src/main/java/
│   │   ├── entity/            # 数据库实体
│   │   ├── repository/        # 数据访问层
│   │   ├── service/           # 业务逻辑层
│   │   ├── controller/        # API控制器
│   │   └── model/             # 数据传输对象
│   ├── src/main/resources/
│   └── pom.xml
│
├── datafeed-react/            # 前端React项目
│   ├── src/
│   │   ├── services/          # API服务
│   │   ├── hooks/             # 自定义Hooks
│   │   ├── pages/             # 页面组件
│   │   └── components/        # 通用组件
│   └── package.json
│
├── start-all.bat              # 启动所有服务
├── start-backend.bat          # 启动后端服务
├── start-frontend.bat         # 启动前端应用
├── service-status.html        # 服务状态检查
├── test-apis.html             # API测试页面
└── DATAFEED_README.md         # DataFeed项目说明
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :8082
   netstat -ano | findstr :3000
   ```

2. **后端启动失败**
   - 检查Java版本：`java -version`
   - 检查Maven版本：`mvn -version`
   - 查看启动日志

3. **前端启动失败**
   - 检查Node.js版本：`node -version`
   - 重新安装依赖：`npm install`
   - 清除缓存：`npm start -- --reset-cache`

4. **API无法访问**
   - 确认后端服务已启动
   - 检查防火墙设置
   - 验证CORS配置

### 服务状态检查

访问 [service-status.html](file:///W:/gitRep/monitor-dashboard/service-status.html) 页面可以：
- 实时检查前后端服务状态
- 测试所有API接口
- 快速打开相关页面

## 📈 功能演示

1. **启动服务**: 运行 `start-all.bat`
2. **访问应用**: 打开 http://localhost:3000
3. **查看仪表板**: 实时监控数据和图表
4. **数据统计**: 切换到数据统计页面，选择时间单位和范围
5. **API测试**: 访问 test-apis.html 测试所有接口

## 🎯 下一步计划

- [ ] 集成真实数据库
- [ ] 添加用户认证
- [ ] 实现告警系统
- [ ] 性能优化
- [ ] 部署配置

---

**项目状态**: ✅ 开发完成，功能正常运行  
**最后更新**: 2025-06-20
