package com.innodealing.onshore.monitor.service;

import com.innodealing.onshore.monitor.demo.RetryDemoMonitor;
import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试execute方法拆分功能
 */
@SpringBootTest
@ActiveProfiles("local")
class ExecuteMethodSplitTest {

    @Resource
    private MonitorService monitorService;

    @Test
    void testExecuteSync() {
        System.out.println("=== 测试同步执行方法 ===");
        
        RetryDemoMonitor.resetCallCount();
        
        // 使用新的同步执行方法
        MonitorExecuteInfo result = monitorService.executeSync("retryDemoMonitor", false);
        
        // 验证结果
        assertNotNull(result, "执行结果不应为空");
        assertNotNull(result.getJobId(), "任务ID不应为空");
        assertNotNull(result.getHandleTime(), "处理时间不应为空");
        
        // 同步执行应该返回最终结果
        assertTrue(result.getHandleCode() == HandleCode.SUCCESS || 
                  result.getHandleCode() == HandleCode.FAIL || 
                  result.getHandleCode() == HandleCode.EXCEPTION, 
                  "同步执行应该返回最终状态");
        
        // 验证监控器被调用
        assertTrue(RetryDemoMonitor.getCallCount() > 0, "监控器应该被调用");
        
        System.out.println("同步执行结果:");
        System.out.println("  任务ID: " + result.getJobId());
        System.out.println("  状态: " + result.getHandleCode());
        System.out.println("  监控器调用次数: " + RetryDemoMonitor.getCallCount());
        System.out.println("✅ 同步执行方法测试通过");
    }

    @Test
    void testExecuteAsync() throws InterruptedException {
        System.out.println("=== 测试异步执行方法 ===");
        
        RetryDemoMonitor.resetCallCount();
        
        // 使用新的异步执行方法
        MonitorExecuteInfo result = monitorService.executeAsync("retryDemoMonitor", false);
        
        // 验证初始结果
        assertNotNull(result, "执行结果不应为空");
        assertNotNull(result.getJobId(), "任务ID不应为空");
        assertNotNull(result.getTriggerTime(), "触发时间不应为空");
        
        // 异步执行应该立即返回RUNNING状态
        assertEquals(HandleCode.RUNNING, result.getHandleCode().intValue(), "异步执行应该立即返回RUNNING状态");
        
        System.out.println("异步执行初始结果:");
        System.out.println("  任务ID: " + result.getJobId());
        System.out.println("  初始状态: " + result.getHandleCode());
        
        // 等待一段时间让异步任务执行
        Thread.sleep(3000);
        
        // 通过jobId获取最新状态
        MonitorExecuteInfo updatedResult = monitorService.getJobInfo(result.getJobId());
        assertNotNull(updatedResult, "应该能够获取到任务信息");
        
        // 验证监控器被调用
        assertTrue(RetryDemoMonitor.getCallCount() > 0, "监控器应该被调用");
        
        System.out.println("异步执行最终结果:");
        System.out.println("  最终状态: " + updatedResult.getHandleCode());
        System.out.println("  监控器调用次数: " + RetryDemoMonitor.getCallCount());
        System.out.println("✅ 异步执行方法测试通过");
    }

    @Test
    void testExecuteWithAlarm() {
        System.out.println("=== 测试带告警的执行 ===");
        
        // 测试同步执行带告警
        MonitorExecuteInfo syncResult = monitorService.executeSync("retryDemoMonitor", true);
        assertNotNull(syncResult, "同步执行结果不应为空");
        
        // 测试异步执行带告警
        MonitorExecuteInfo asyncResult = monitorService.executeAsync("retryDemoMonitor", true);
        assertNotNull(asyncResult, "异步执行结果不应为空");
        assertEquals(HandleCode.RUNNING, asyncResult.getHandleCode().intValue(), "异步执行应该返回RUNNING状态");
        
        System.out.println("带告警执行测试:");
        System.out.println("  同步执行状态: " + syncResult.getHandleCode());
        System.out.println("  异步执行状态: " + asyncResult.getHandleCode());
        System.out.println("✅ 带告警的执行测试通过");
    }

    @Test
    void testBackwardCompatibility() {
        System.out.println("=== 测试向后兼容性 ===");
        
        RetryDemoMonitor.resetCallCount();
        
        // 测试原有的execute方法仍然工作
        MonitorExecuteInfo syncResult = monitorService.execute("retryDemoMonitor", false, false);
        assertNotNull(syncResult, "同步执行结果不应为空");
        
        MonitorExecuteInfo asyncResult = monitorService.execute("retryDemoMonitor", true, false);
        assertNotNull(asyncResult, "异步执行结果不应为空");
        assertEquals(HandleCode.RUNNING, asyncResult.getHandleCode().intValue(), "异步执行应该返回RUNNING状态");
        
        System.out.println("向后兼容性测试:");
        System.out.println("  原方法同步执行状态: " + syncResult.getHandleCode());
        System.out.println("  原方法异步执行状态: " + asyncResult.getHandleCode());
        System.out.println("✅ 向后兼容性测试通过");
    }

    @Test
    void testMethodComparison() throws InterruptedException {
        System.out.println("=== 测试新旧方法对比 ===");
        
        // 重置计数器
        RetryDemoMonitor.resetCallCount();
        
        // 使用原方法同步执行
        MonitorExecuteInfo oldSyncResult = monitorService.execute("retryDemoMonitor", false, false);
        int oldSyncCallCount = RetryDemoMonitor.getCallCount();
        
        // 重置计数器
        RetryDemoMonitor.resetCallCount();
        
        // 使用新方法同步执行
        MonitorExecuteInfo newSyncResult = monitorService.executeSync("retryDemoMonitor", false);
        int newSyncCallCount = RetryDemoMonitor.getCallCount();
        
        // 验证同步执行结果一致
        assertEquals(oldSyncResult.getHandleCode(), newSyncResult.getHandleCode(), "新旧同步方法应该返回相同的状态");
        
        // 重置计数器
        RetryDemoMonitor.resetCallCount();
        
        // 使用原方法异步执行
        MonitorExecuteInfo oldAsyncResult = monitorService.execute("retryDemoMonitor", true, false);
        
        // 重置计数器
        RetryDemoMonitor.resetCallCount();
        
        // 使用新方法异步执行
        MonitorExecuteInfo newAsyncResult = monitorService.executeAsync("retryDemoMonitor", false);
        
        // 验证异步执行初始状态一致
        assertEquals(oldAsyncResult.getHandleCode(), newAsyncResult.getHandleCode(), "新旧异步方法应该返回相同的初始状态");
        
        System.out.println("方法对比结果:");
        System.out.println("  原同步方法状态: " + oldSyncResult.getHandleCode() + ", 调用次数: " + oldSyncCallCount);
        System.out.println("  新同步方法状态: " + newSyncResult.getHandleCode() + ", 调用次数: " + newSyncCallCount);
        System.out.println("  原异步方法状态: " + oldAsyncResult.getHandleCode());
        System.out.println("  新异步方法状态: " + newAsyncResult.getHandleCode());
        System.out.println("✅ 新旧方法对比测试通过");
    }

    @Test
    void testNonExistentMonitor() {
        System.out.println("=== 测试不存在的监控器 ===");
        
        // 测试同步执行不存在的监控器
        assertThrows(UnsupportedOperationException.class, () -> {
            monitorService.executeSync("nonExistentMonitor", false);
        }, "同步执行不存在的监控器应该抛出异常");
        
        // 测试异步执行不存在的监控器
        assertThrows(UnsupportedOperationException.class, () -> {
            monitorService.executeAsync("nonExistentMonitor", false);
        }, "异步执行不存在的监控器应该抛出异常");
        
        System.out.println("✅ 不存在的监控器测试通过");
    }
}
