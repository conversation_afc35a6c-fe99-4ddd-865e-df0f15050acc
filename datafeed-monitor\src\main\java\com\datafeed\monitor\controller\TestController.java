package com.datafeed.monitor.controller;

import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {
    
    /**
     * 简单的测试接口
     */
    @GetMapping("/hello")
    public Map<String, Object> hello() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Hello from DataFeed Monitor!");
        result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.put("status", "success");
        return result;
    }
    
    /**
     * 测试POST接口
     */
    @PostMapping("/echo")
    public Map<String, Object> echo(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        result.put("received", request);
        result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.put("status", "success");
        return result;
    }
    
    /**
     * 测试参数接口
     */
    @GetMapping("/params")
    public Map<String, Object> testParams(
            @RequestParam(required = false, defaultValue = "default") String name,
            @RequestParam(required = false, defaultValue = "0") Integer count) {
        Map<String, Object> result = new HashMap<>();
        result.put("name", name);
        result.put("count", count);
        result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.put("status", "success");
        return result;
    }
}
