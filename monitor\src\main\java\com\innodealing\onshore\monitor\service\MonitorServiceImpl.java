package com.innodealing.onshore.monitor.service;

import com.innodealing.onshore.monitor.Monitor;
import com.innodealing.onshore.monitor.alter.AlterConsumer;
import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorClient;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.model.MonitorInfo;
import com.innodealing.onshore.monitor.model.MonitorReport;
import com.innodealing.onshore.monitor.model.TriggerCode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

@Service
public class MonitorServiceImpl implements MonitorService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final Map<String, List<MonitorExecuteInfo>> monitorJobsMap = new ConcurrentHashMap<>();
    private final Map<String, MonitorExecuteInfo> jobMap = new ConcurrentHashMap<>();
    private final Deque<MonitorExecuteInfo> monitorExecuteInfoQueue = new LinkedBlockingDeque<>();
    private static final long CLEAR_RANGE = 1000L * 3600L * 168L;
    @Resource
    private MonitorMetaService monitorMetaService;
    @Resource
    private MonitorAlterService monitorAlterService;
    @Resource
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;
    @Value("${monitor.schedule.enable}")
    private Boolean scheduleEnable;
    @Value("${monitor.retry.maxCount:2}")
    private Integer maxRetryCount;
    @Value("${monitor.retry.delaySeconds:10}")
    private Integer retryDelaySeconds;

    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 50, 2, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000));

    private Integer jobIdPeek = 0;

    @PostConstruct
    public void init() {
        schedulesAll();
    }

    @Override
    public String generateJobId() {
        return "" + jobIdPeek++;
    }

    @Override
    public MonitorExecuteInfo execute(String monitorName) {
        return execute(monitorName, false, false);
    }

    @Override
    public MonitorExecuteInfo execute(Class<? extends Monitor> monitorClass) {
        return execute(monitorMetaService.getMonitorNameByClass(monitorClass));
    }

    /**
     * 设计:
     * 1.定义任务信息
     * 2.提交任务
     * 3.根据async状态判断是否要等待结果
     *
     * @deprecated 建议使用 executeSync 或 executeAsync 方法
     */
    @Override
    public MonitorExecuteInfo execute(String monitorName, boolean async, boolean alarm) {
        return executeInternal(monitorName, alarm, !async);
    }

    /**
     * 内部执行方法，统一处理同步和异步执行逻辑
     *
     * @param monitorName 监控器名称
     * @param alarm       是否启用告警
     * @param sync        是否同步执行
     * @return 监控执行信息
     */
    private MonitorExecuteInfo executeInternal(String monitorName, boolean alarm, boolean sync) {
        // 1. 验证监控器存在性
        final MonitorInfo monitorInfo = monitorMetaService.getMonitorInfo(monitorName);
        if (Objects.isNull(monitorInfo)) {
            throw new UnsupportedOperationException("该监视器不存在");
        }

        // 2. 准备执行环境
        final Monitor monitor = monitorInfo.getMonitor();
        final MonitorClient monitorClient = monitorInfo.getMonitorClient();
        final Class<? extends AlterConsumer> alterConsumer = monitorClient.alterConsumer();
        final String jobId = generateJobId();

        // 3. 创建执行信息
        final MonitorExecuteInfo executeInfo = new MonitorExecuteInfo(
                jobId,
                monitorInfo.getMonitorName(),
                monitorClient.desc(),
                monitorClient.group(),
                TriggerCode.SUCCESS,
                HandleCode.RUNNING,
                new Timestamp(System.currentTimeMillis()),
                null,
                null
        );
        executeInfo.setMaxRetryCount(maxRetryCount);
        addJobInfo(executeInfo);

        // 4. 提交任务执行
        final Future<MonitorExecuteInfo> infoFuture = threadPoolExecutor.submit(
                () -> executeMonitorWithRetry(executeInfo, monitor, monitorClient, alarm)
        );

        // 5. 根据执行模式处理结果
        if (sync) {
            // 同步执行：等待结果
            try {
                return infoFuture.get(120, TimeUnit.SECONDS);
            } catch (Exception e) {
                infoFuture.cancel(false);
                logger.error("monitor timeout in sync execution jobId:{}", jobId, e);
                executeInfo.setHandleCode(HandleCode.EXCEPTION);
                executeInfo.setHandleTime(new Timestamp(System.currentTimeMillis()));
                if (alarm) {
                    monitorAlterService.addAlarm(executeInfo, alterConsumer);
                }
                return executeInfo;
            }
        } else {
            // 异步执行：立即返回
            logger.debug("Async execution started for monitor: {}, jobId: {}", monitorName, jobId);
            return executeInfo;
        }
    }

    @Override
    public List<MonitorExecuteInfo> executeGroups(String[] groups) {
        List<String> groupNames = Arrays.asList(groups);
        return monitorMetaService.listAllMonitorInfos()
                .stream()
                .filter(v -> groupNames.stream().anyMatch(groupName -> v.getMonitorClient().group().startsWith(groupName)))
                .map(monitorInfo -> execute(monitorInfo.getMonitorName()))
                .sorted(Comparator.comparing(MonitorExecuteInfo::getGroup))
                .collect(Collectors.toList());
    }

    @Override
    public List<MonitorExecuteInfo> executeAll() {
        return monitorMetaService.listAllMonitorInfos()
                .stream()
                .map(monitorInfo -> execute(monitorInfo.getMonitorName()))
                .collect(Collectors.toList());
    }

    @Override
    public void schedule(String monitorName) {
        MonitorInfo monitorInfo = monitorMetaService.getMonitorInfo(monitorName);
        MonitorClient monitorClient = monitorInfo.getMonitorClient();
        threadPoolTaskScheduler.schedule(() -> executeInternal(monitorName, true, true), new CronTrigger(monitorClient.schedule()));
    }

    @Override
    public void schedules(String[] monitorNames) {
        List<MonitorInfo> monitorInfos = Arrays.stream(monitorNames)
                .filter(StringUtils::isNotBlank)
                .map(v -> monitorMetaService.getMonitorInfo(v))
                .filter(v -> Objects.nonNull(v) && StringUtils.isNotBlank(v.getMonitorClient().schedule()))
                .collect(Collectors.toList());
        if (monitorInfos.size() - monitorNames.length != 0) {
            throw new UnsupportedOperationException("指定的监视器不存在或者监视器缺少corn表达式");
        }
        for (MonitorInfo monitorInfo : monitorInfos) {
            schedule(monitorInfo.getMonitorName());
        }
    }

    @Override
    public void schedulesAll() {
        if (!scheduleEnable) {
            logger.info("schedule enable is false");
            return;
        }
        String[] names = monitorMetaService.listAllMonitorInfos().stream().filter(v -> StringUtils.isNotBlank(v.getMonitorClient().schedule())).map(MonitorInfo::getMonitorName)
                .toArray(String[]::new);
        schedules(names);
    }


    @Override
    public List<MonitorExecuteInfo> jobs(String... jobIds) {
        return Arrays.stream(jobIds)
                .map(jobId -> jobMap.getOrDefault(jobId, null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<MonitorExecuteInfo> jobsByMonitor(String... monitors) {
        List<MonitorExecuteInfo> monitorExecuteInfos = new ArrayList<>();
        for (String monitor : monitors) {
            monitorExecuteInfos.addAll(monitorJobsMap.getOrDefault(monitor, new ArrayList<>()));
        }
        return monitorExecuteInfos
                .stream()
                .sorted(Comparator.comparing(MonitorExecuteInfo::getTriggerTime))
                .collect(Collectors.toList());
    }

    @Override
    public MonitorExecuteInfo getJobInfo(String jobId) {
        return jobMap.get(jobId);
    }

    @Override
    public List<MonitorExecuteInfo> listMonitorInfos(String monitorName) {
        return monitorJobsMap.get(monitorName);
    }


    private void addJobInfo(MonitorExecuteInfo monitorExecuteInfo) {
        List<MonitorExecuteInfo> monitorExecuteInfos = monitorJobsMap.get(monitorExecuteInfo.getName());
        if (Objects.isNull(monitorExecuteInfos)) {
            monitorExecuteInfos = new ArrayList<>();
            monitorExecuteInfos.add(monitorExecuteInfo);
            monitorJobsMap.put(monitorExecuteInfo.getName(), monitorExecuteInfos);
        }else {
            monitorExecuteInfos.add(monitorExecuteInfo);
        }
        jobMap.put(monitorExecuteInfo.getJobId(), monitorExecuteInfo);
        monitorExecuteInfoQueue.push(monitorExecuteInfo);
    }

    private void removeJobInfo(MonitorExecuteInfo monitorExecuteInfo) {
        monitorJobsMap.remove(monitorExecuteInfo.getJobId());
        jobMap.remove(monitorExecuteInfo.getJobId());
    }

    @Scheduled(initialDelay = 30_000L, fixedDelay = 3600_000L)
    public void deleteHistoryInfos() {
        int deletedCount = 0;
        while (!monitorExecuteInfoQueue.isEmpty()) {
            MonitorExecuteInfo executeInfo = monitorExecuteInfoQueue.peekFirst();
            if (Objects.isNull(executeInfo) || System.currentTimeMillis() - executeInfo.getTriggerTime().getTime() < CLEAR_RANGE) {
                logger.info("deleteHistoryInfos 未到达清理时间");
                break;
            } else {
                removeJobInfo(monitorExecuteInfoQueue.pop());
                deletedCount++;
                logger.info("deleteHistoryInfos remove,jobId:{},monitorName:{},triggerTime:{}", executeInfo.getJobId(), executeInfo.getName(), executeInfo.getTriggerTime());
            }
        }
        logger.info("deleteHistoryInfos deleted:{},remaining:{}", deletedCount, monitorExecuteInfoQueue.size() - deletedCount);
    }

    /**
     * 执行监控任务，包含重试逻辑
     */
    private MonitorExecuteInfo executeMonitorWithRetry(MonitorExecuteInfo executeInfo, Monitor monitor, MonitorClient monitorClient, boolean alarm) {
        return executeMonitorWithRetryInternal(executeInfo, monitor, monitorClient, alarm, 0);
    }

    /**
     * 内部执行监控任务，包含重试逻辑
     */
    private MonitorExecuteInfo executeMonitorWithRetryInternal(MonitorExecuteInfo executeInfo, Monitor monitor, MonitorClient monitorClient, boolean alarm, int currentRetry) {
        int handleCode = executeInfo.getHandleCode();
        // 不是RUNNING说明已经执行过了 直接返回
        if (handleCode != HandleCode.RUNNING) {
            return executeInfo;
        }

        // 核心校验逻辑
        MonitorReport monitorReport = null;
        try {
            monitorReport = monitor.reportMonitor();
            handleCode = monitorReport.isAccess() ? HandleCode.SUCCESS : HandleCode.FAIL;
        } catch (Exception e) {
            logger.error("monitor exception jobId:{}, retry:{}", executeInfo.getJobId(), currentRetry, e);
            handleCode = HandleCode.EXCEPTION;
        }

        // 设置执行结果
        executeInfo.setHandleCode(handleCode);
        executeInfo.setHandleTime(new Timestamp(System.currentTimeMillis()));
        executeInfo.setMonitorReport(monitorReport);

        // 如果成功的话直接返回
        if (Objects.equals(handleCode, HandleCode.SUCCESS)) {
            logger.debug("monitor execution success jobId:{},retryCount:{}", executeInfo.getJobId(), executeInfo.getRetryCount());
            return executeInfo;
        }

        // 如果执行失败且可以重试，则进行重试
        if (handleCode != HandleCode.SUCCESS && currentRetry < executeInfo.getMaxRetryCount()) {
            logger.warn("monitor execution failed jobId:{}, currentRetry:{}, attempting retry", executeInfo.getJobId(), currentRetry);

            // 更新重试信息
            executeInfo.setHandleCode(HandleCode.RUNNING);
            // 记录重试信息
            MonitorExecuteInfo.RetryRecord retryRecord = new MonitorExecuteInfo.RetryRecord(
                    executeInfo.getRetryCount(),
                    new Timestamp(System.currentTimeMillis()),
                    "Task failed, attempting retry"
            );
            executeInfo.getRetryHistory().add(retryRecord);

            logger.info("Starting retry {} for jobId:{}, delaying {} seconds",
                    executeInfo.getRetryCount(), executeInfo.getJobId(), retryDelaySeconds);

            try {
                // 延迟重试
                Thread.sleep(retryDelaySeconds * 1000L);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                logger.warn("Retry delay interrupted for jobId:{}", executeInfo.getJobId());
            }

            // 递归调用进行重试
            return executeMonitorWithRetryInternal(executeInfo, monitor, monitorClient, alarm, executeInfo.getRetryCount());
        }

        // 如果不能重试或重试次数用完，检查是否需要告警
        if (alarm && handleCode != HandleCode.SUCCESS) {
            monitorAlterService.addAlarm(executeInfo, monitorClient.alterConsumer());
        }
        return executeInfo;
    }


}
