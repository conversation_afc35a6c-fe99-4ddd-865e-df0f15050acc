package com.innodealing.skywalking.sdk.service;

import com.innodealing.skywalking.sdk.config.SkywalkingProperties;
import com.innodealing.skywalking.sdk.module.ErrorLogDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * SkyWalking主服务类
 */
public class SkywalkingService {
    
    private static final Logger logger = LoggerFactory.getLogger(SkywalkingService.class);
    
    private final SkywalkingProperties properties;
    private final List<SkywalkingErrorLogHandler> errorLogHandlers;

    public SkywalkingService(SkywalkingProperties properties, List<SkywalkingErrorLogHandler> errorLogHandlers) {
        this.properties = properties;
        this.errorLogHandlers = errorLogHandlers;
    }

    /**
     * 检查指定服务的错误日志
     *
     * @param serviceId 服务ID
     * @param timeRange 时间范围 ("today", "last_hour", 或其他)
     * @return 是否有错误
     */
    public boolean checkServiceErrors(Integer serviceId, String timeRange) {
        try {
            Optional<SkywalkingProperties.SkywalkingService> serviceOpt = properties.getServices().stream()
                    .filter(s -> s.getId().equals(serviceId))
                    .findFirst();

            if (serviceOpt.isPresent()) {
                SkywalkingProperties.SkywalkingService service = serviceOpt.get();
                LocalDateTime endTime = LocalDateTime.now();
                LocalDateTime startTime = calculateStartTime(endTime, timeRange);

                return hasErrors(service, startTime, endTime);
            }
            return false;
        } catch (Exception e) {
            logger.error("检查服务错误失败: serviceId={}", serviceId, e);
            return false;
        }
    }

    /**
     * 检查所有服务的错误日志
     *
     * @param timeRange 时间范围
     * @return 服务检查结果列表
     */
    public List<ServiceCheckResult> checkAllServicesErrors(String timeRange) {
        return properties.getServices().stream()
                .map(service -> {
                    ServiceCheckResult result = new ServiceCheckResult();
                    result.setId(service.getId());
                    result.setServerName(service.getServerName());
                    
                    try {
                        LocalDateTime endTime = LocalDateTime.now();
                        LocalDateTime startTime = calculateStartTime(endTime, timeRange);
                        boolean hasError = hasErrors(service, startTime, endTime);
                        result.setSuccess(!hasError);
                    } catch (Exception e) {
                        logger.error("检查服务错误失败: {}", service.getServerName(), e);
                        result.setSuccess(false);
                        result.setMessage("检查服务出错: " + e.getMessage());
                    }
                    
                    return result;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取服务错误详情
     *
     * @param serviceId 服务ID
     * @param timeRange 时间范围
     * @return 错误详情
     */
    public ServiceErrorDetails getServiceErrorDetails(Integer serviceId, String timeRange) {
        ServiceErrorDetails details = new ServiceErrorDetails();
        details.setId(serviceId);

        try {
            Optional<SkywalkingProperties.SkywalkingService> serviceOpt = properties.getServices().stream()
                    .filter(s -> s.getId().equals(serviceId))
                    .findFirst();

            if (serviceOpt.isPresent()) {
                SkywalkingProperties.SkywalkingService service = serviceOpt.get();
                details.setServerName(service.getServerName());
                
                LocalDateTime endTime = LocalDateTime.now();
                LocalDateTime startTime = calculateStartTime(endTime, timeRange);

                for (SkywalkingErrorLogHandler handler : errorLogHandlers) {
                    Optional<ErrorLogDTO> errorLogOpt = handler.getErrorLog(
                            service.getServerName(),
                            service.getId(),
                            service.getIgnoreUrls(),
                            startTime,
                            endTime
                    );

                    if (errorLogOpt.isPresent()) {
                        ErrorLogDTO errorLog = errorLogOpt.get();
                        details.addErrorLog(errorLog);
                    }
                }
            } else {
                details.setMessage("服务不存在");
            }
        } catch (Exception e) {
            logger.error("获取服务错误详情失败: serviceId={}", serviceId, e);
            details.setMessage("获取错误详情失败: " + e.getMessage());
        }

        return details;
    }

    private boolean hasErrors(SkywalkingProperties.SkywalkingService service, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            for (SkywalkingErrorLogHandler handler : errorLogHandlers) {
                Optional<ErrorLogDTO> errorLogOpt = handler.getErrorLog(
                        service.getServerName(),
                        service.getId(),
                        service.getIgnoreUrls(),
                        startTime,
                        endTime
                );

                if (errorLogOpt.isPresent()) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            logger.error("检查错误失败", e);
            return false;
        }
    }

    private LocalDateTime calculateStartTime(LocalDateTime endTime, String timeRange) {
        if ("today".equals(timeRange)) {
            return endTime.toLocalDate().atStartOfDay();
        } else if ("last_hour".equals(timeRange)) {
            return endTime.minusHours(1);
        } else {
            return endTime.minusHours(1);
        }
    }

    /**
     * 服务检查结果
     */
    public static class ServiceCheckResult {
        private Integer id;
        private String serverName;
        private boolean success;
        private String message;

        // getters and setters
        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        public String getServerName() { return serverName; }
        public void setServerName(String serverName) { this.serverName = serverName; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * 服务错误详情
     */
    public static class ServiceErrorDetails {
        private Integer id;
        private String serverName;
        private String message;
        private List<ErrorLogDTO> errorLogs = new java.util.ArrayList<>();

        public void addErrorLog(ErrorLogDTO errorLog) {
            this.errorLogs.add(errorLog);
        }

        // getters and setters
        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        public String getServerName() { return serverName; }
        public void setServerName(String serverName) { this.serverName = serverName; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public List<ErrorLogDTO> getErrorLogs() { return errorLogs; }
        public void setErrorLogs(List<ErrorLogDTO> errorLogs) { this.errorLogs = errorLogs; }
    }
}
