package com.datafeed.monitor.model;

import java.util.List;

/**
 * 仪表板数据
 */
public class DashboardData {
    
    /**
     * 在线用户数量
     */
    private Integer onlineUserCount;
    
    /**
     * 在线用户列表
     */
    private List<String> onlineUsers;
    
    /**
     * 今日发送数量
     */
    private Integer todaySendCount;
    
    /**
     * 失败数量
     */
    private Integer failureCount;
    
    /**
     * 成功率
     */
    private Double successRate;
    
    /**
     * 趋势数据
     */
    private List<TrendDataPoint> trendData;
    
    /**
     * 产品统计数据
     */
    private List<ProductStatistics> productStatistics;
    
    public DashboardData() {}
    
    public Integer getOnlineUserCount() {
        return onlineUserCount;
    }
    
    public void setOnlineUserCount(Integer onlineUserCount) {
        this.onlineUserCount = onlineUserCount;
    }
    
    public List<String> getOnlineUsers() {
        return onlineUsers;
    }
    
    public void setOnlineUsers(List<String> onlineUsers) {
        this.onlineUsers = onlineUsers;
    }
    
    public Integer getTodaySendCount() {
        return todaySendCount;
    }
    
    public void setTodaySendCount(Integer todaySendCount) {
        this.todaySendCount = todaySendCount;
    }
    
    public Integer getFailureCount() {
        return failureCount;
    }
    
    public void setFailureCount(Integer failureCount) {
        this.failureCount = failureCount;
    }
    
    public Double getSuccessRate() {
        return successRate;
    }
    
    public void setSuccessRate(Double successRate) {
        this.successRate = successRate;
    }
    
    public List<TrendDataPoint> getTrendData() {
        return trendData;
    }
    
    public void setTrendData(List<TrendDataPoint> trendData) {
        this.trendData = trendData;
    }
    
    public List<ProductStatistics> getProductStatistics() {
        return productStatistics;
    }
    
    public void setProductStatistics(List<ProductStatistics> productStatistics) {
        this.productStatistics = productStatistics;
    }
    
    @Override
    public String toString() {
        return "DashboardData{" +
                "onlineUserCount=" + onlineUserCount +
                ", onlineUsers=" + onlineUsers +
                ", todaySendCount=" + todaySendCount +
                ", failureCount=" + failureCount +
                ", successRate=" + successRate +
                ", trendData=" + trendData +
                ", productStatistics=" + productStatistics +
                '}';
    }
}
