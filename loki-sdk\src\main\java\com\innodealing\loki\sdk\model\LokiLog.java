package com.innodealing.loki.sdk.model;

import java.util.List;

/**
 * Loki日志响应
 */
public class LokiLog {
    private String status;
    private Data data;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {
        private String resultType;
        private List<Result> result;
        private Stats stats;

        public String getResultType() {
            return resultType;
        }

        public void setResultType(String resultType) {
            this.resultType = resultType;
        }

        public List<Result> getResult() {
            return result;
        }

        public void setResult(List<Result> result) {
            this.result = result;
        }

        public Stats getStats() {
            return stats;
        }

        public void setStats(Stats stats) {
            this.stats = stats;
        }

        public static class Result {
            private Stream stream;
            private List<List<String>> values;

            public Stream getStream() {
                return stream;
            }

            public void setStream(Stream stream) {
                this.stream = stream;
            }

            public List<List<String>> getValues() {
                return values;
            }

            public void setValues(List<List<String>> values) {
                this.values = values;
            }

            public static class Stream {
                private String app;
                private String filename;
                private String hostname;

                public String getApp() {
                    return app;
                }

                public void setApp(String app) {
                    this.app = app;
                }

                public String getFilename() {
                    return filename;
                }

                public void setFilename(String filename) {
                    this.filename = filename;
                }

                public String getHostname() {
                    return hostname;
                }

                public void setHostname(String hostname) {
                    this.hostname = hostname;
                }
            }
        }

        public static class Stats {
            private Summary summary;
            private Querier querier;
            private Ingester ingester;

            public Summary getSummary() {
                return summary;
            }

            public void setSummary(Summary summary) {
                this.summary = summary;
            }

            public Querier getQuerier() {
                return querier;
            }

            public void setQuerier(Querier querier) {
                this.querier = querier;
            }

            public Ingester getIngester() {
                return ingester;
            }

            public void setIngester(Ingester ingester) {
                this.ingester = ingester;
            }

            public static class Summary {
                private long bytesProcessedPerSecond;
                private long linesProcessedPerSecond;
                private long totalBytesProcessed;
                private long totalLinesProcessed;
                private double execTime;
                private double queueTime;
                private int subqueries;
                private int totalEntriesReturned;

                public long getBytesProcessedPerSecond() {
                    return bytesProcessedPerSecond;
                }

                public void setBytesProcessedPerSecond(long bytesProcessedPerSecond) {
                    this.bytesProcessedPerSecond = bytesProcessedPerSecond;
                }

                public long getLinesProcessedPerSecond() {
                    return linesProcessedPerSecond;
                }

                public void setLinesProcessedPerSecond(long linesProcessedPerSecond) {
                    this.linesProcessedPerSecond = linesProcessedPerSecond;
                }

                public long getTotalBytesProcessed() {
                    return totalBytesProcessed;
                }

                public void setTotalBytesProcessed(long totalBytesProcessed) {
                    this.totalBytesProcessed = totalBytesProcessed;
                }

                public long getTotalLinesProcessed() {
                    return totalLinesProcessed;
                }

                public void setTotalLinesProcessed(long totalLinesProcessed) {
                    this.totalLinesProcessed = totalLinesProcessed;
                }

                public double getExecTime() {
                    return execTime;
                }

                public void setExecTime(double execTime) {
                    this.execTime = execTime;
                }

                public double getQueueTime() {
                    return queueTime;
                }

                public void setQueueTime(double queueTime) {
                    this.queueTime = queueTime;
                }

                public int getSubqueries() {
                    return subqueries;
                }

                public void setSubqueries(int subqueries) {
                    this.subqueries = subqueries;
                }

                public int getTotalEntriesReturned() {
                    return totalEntriesReturned;
                }

                public void setTotalEntriesReturned(int totalEntriesReturned) {
                    this.totalEntriesReturned = totalEntriesReturned;
                }
            }

            // 简化其他内部类，只保留核心功能
            public static class Querier {
                // 简化实现
            }

            public static class Ingester {
                // 简化实现
            }
        }
    }
}
