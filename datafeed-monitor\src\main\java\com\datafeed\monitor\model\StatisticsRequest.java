package com.datafeed.monitor.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 统计查询请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StatisticsRequest {
    
    /**
     * 统计单位: minute 或 second
     */
    @NotNull(message = "统计单位不能为空")
    @Pattern(regexp = "^(minute|second)$", message = "统计单位只能是 minute 或 second")
    private String timeUnit;
    
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
    
    /**
     * 数据源（可选）
     */
    private String dataSource;
    
    /**
     * 目标系统（可选）
     */
    private String targetSystem;
}
