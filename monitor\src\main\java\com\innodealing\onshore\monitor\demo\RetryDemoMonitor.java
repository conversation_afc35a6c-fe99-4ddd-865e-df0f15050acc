package com.innodealing.onshore.monitor.demo;

import com.innodealing.onshore.monitor.Monitor;
import com.innodealing.onshore.monitor.alter.FeishuAlterConsumer;
import com.innodealing.onshore.monitor.model.MonitorClient;
import com.innodealing.onshore.monitor.model.MonitorReport;
import com.innodealing.onshore.monitor.report.DefaultReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 重试功能演示监控器
 * 用于演示监控服务的重试机制
 */
@MonitorClient(
    desc = "重试功能演示监控器",
    enable = true, // 启用用于测试
    alterConsumer = FeishuAlterConsumer.class,
    group = "演示"
)
public class RetryDemoMonitor implements Monitor {
    
    private static final Logger logger = LoggerFactory.getLogger(RetryDemoMonitor.class);
    private static final AtomicInteger callCount = new AtomicInteger(0);
    
    @Override
    public boolean monitor() {
        return reportMonitor().isAccess();
    }

    @Override
    public MonitorReport reportMonitor() {
        int count = callCount.incrementAndGet();
        logger.info("RetryDemoMonitor 被调用，第 {} 次", count);
        
        // 前两次调用模拟失败，第三次成功
        if (count <= 2) {
            logger.warn("RetryDemoMonitor 第 {} 次调用模拟失败", count);
            throw new RuntimeException("模拟监控失败，用于演示重试机制 - 第" + count + "次调用");
        }
        
        logger.info("RetryDemoMonitor 第 {} 次调用成功", count);
        return new MonitorReport(true, DefaultReport.success());
    }
    
    /**
     * 重置调用计数器，用于测试
     */
    public static void resetCallCount() {
        callCount.set(0);
    }
    
    /**
     * 获取调用次数，用于测试验证
     */
    public static int getCallCount() {
        return callCount.get();
    }
}
