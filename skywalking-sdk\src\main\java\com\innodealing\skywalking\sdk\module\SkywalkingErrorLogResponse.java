package com.innodealing.skywalking.sdk.module;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * SkyWalking错误日志响应
 */
public class SkywalkingErrorLogResponse {
    private int total;
    private int ignoreTotal;
    private String serverName;
    private Set<Detail> details;

    public void addIgnoreTotal() {
        ignoreTotal++;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public int getIgnoreTotal() {
        return ignoreTotal;
    }

    public void setIgnoreTotal(int ignoreTotal) {
        this.ignoreTotal = ignoreTotal;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public Set<Detail> getDetails() {
        return Objects.isNull(details) ? new HashSet<>() : new HashSet<>(details);
    }

    public void setDetails(Set<Detail> details) {
        this.details = Objects.isNull(details) ? new HashSet<>() : new HashSet<>(details);
    }

    public static class Detail {
        private String endpointName;
        private String traceId;
        private String errorTitle;
        private String errorMessage;

        public String getErrorTitle() {
            return errorTitle;
        }

        public void setErrorTitle(String errorTitle) {
            this.errorTitle = errorTitle;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getEndpointName() {
            return endpointName;
        }

        public void setEndpointName(String endpointName) {
            this.endpointName = endpointName;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Detail detail = (Detail) o;
            return Objects.equals(endpointName, detail.endpointName) && Objects.equals(errorMessage, detail.errorMessage);
        }

        @Override
        public int hashCode() {
            return Objects.hash(endpointName, errorMessage);
        }

        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }
    }
}
