package com.datafeed.monitor.model;

/**
 * 产品统计数据
 */
public class ProductStatistics {
    
    /**
     * 产品代码
     */
    private String productCode;
    
    /**
     * 数量
     */
    private Integer count;
    
    /**
     * 百分比
     */
    private Double percentage;
    
    public ProductStatistics() {}
    
    public ProductStatistics(String productCode, Integer count, Double percentage) {
        this.productCode = productCode;
        this.count = count;
        this.percentage = percentage;
    }
    
    public String getProductCode() {
        return productCode;
    }
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public Integer getCount() {
        return count;
    }
    
    public void setCount(Integer count) {
        this.count = count;
    }
    
    public Double getPercentage() {
        return percentage;
    }
    
    public void setPercentage(Double percentage) {
        this.percentage = percentage;
    }
    
    @Override
    public String toString() {
        return "ProductStatistics{" +
                "productCode='" + productCode + '\'' +
                ", count=" + count +
                ", percentage=" + percentage +
                '}';
    }
}
