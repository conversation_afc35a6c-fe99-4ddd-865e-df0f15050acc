package com.datafeed.monitor.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * Datafeed查询请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DatafeedQueryRequest {
    
    /**
     * 时间单位: minute 或 second
     */
    @NotNull(message = "时间单位不能为空")
    @Pattern(regexp = "^(minute|second)$", message = "时间单位只能是 minute 或 second")
    private String timeUnit;
    
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
    
    /**
     * 产品代码（可选）
     */
    private String productCode;
    
    /**
     * 用户账号（可选）
     */
    private String userAccount;
}
