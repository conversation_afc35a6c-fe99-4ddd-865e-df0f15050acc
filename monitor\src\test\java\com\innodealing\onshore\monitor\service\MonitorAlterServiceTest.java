package com.innodealing.onshore.monitor.service;

import com.innodealing.onshore.monitor.alter.LoggerAlterConsumer;
import com.innodealing.onshore.monitor.alter.FeishuAlterConsumer;
import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.model.TriggerCode;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.sql.Timestamp;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MonitorAlterService功能测试
 */
@SpringBootTest
@ActiveProfiles("local")
class MonitorAlterServiceTest {

    @Resource
    private MonitorAlterService monitorAlterService;

    @Test
    void testAddAlarmWithAlterConsumer() {
        System.out.println("=== 测试直接添加告警消费者 ===");
        
        // 清空队列
        monitorAlterService.clearAlarmQueue();
        
        // 创建测试用的监控执行信息
        MonitorExecuteInfo executeInfo = createTestExecuteInfo("testJob1", "TestMonitor", HandleCode.FAIL);
        
        // 创建告警消费者
        LoggerAlterConsumer alterConsumer = new LoggerAlterConsumer(executeInfo);
        
        // 添加告警
        monitorAlterService.addAlarm(alterConsumer);
        
        // 验证队列大小
        assertEquals(1, monitorAlterService.getAlarmQueueSize(), "告警队列应该有1个告警");
        assertFalse(monitorAlterService.isAlarmQueueEmpty(), "告警队列不应该为空");
        
        System.out.println("告警队列大小: " + monitorAlterService.getAlarmQueueSize());
        System.out.println("✅ 直接添加告警消费者测试通过");
    }

    @Test
    void testAddAlarmWithExecuteInfo() {
        System.out.println("=== 测试通过执行信息添加告警 ===");
        
        // 清空队列
        monitorAlterService.clearAlarmQueue();
        
        // 创建测试用的监控执行信息
        MonitorExecuteInfo executeInfo = createTestExecuteInfo("testJob2", "TestMonitor", HandleCode.EXCEPTION);
        
        // 通过执行信息和告警消费者类型添加告警
        monitorAlterService.addAlarm(executeInfo, FeishuAlterConsumer.class);
        
        // 验证队列大小
        assertEquals(1, monitorAlterService.getAlarmQueueSize(), "告警队列应该有1个告警");
        
        System.out.println("告警队列大小: " + monitorAlterService.getAlarmQueueSize());
        System.out.println("✅ 通过执行信息添加告警测试通过");
    }

    @Test
    void testProcessAlarms() {
        System.out.println("=== 测试处理告警队列 ===");
        
        // 清空队列
        monitorAlterService.clearAlarmQueue();
        
        // 添加多个告警
        for (int i = 1; i <= 3; i++) {
            MonitorExecuteInfo executeInfo = createTestExecuteInfo("testJob" + i, "TestMonitor" + i, HandleCode.FAIL);
            monitorAlterService.addAlarm(executeInfo, LoggerAlterConsumer.class);
        }
        
        // 验证添加后的队列大小
        assertEquals(3, monitorAlterService.getAlarmQueueSize(), "告警队列应该有3个告警");
        
        // 手动处理告警
        monitorAlterService.processAlarms();
        
        // 验证处理后的队列大小
        assertEquals(0, monitorAlterService.getAlarmQueueSize(), "告警队列应该为空");
        assertTrue(monitorAlterService.isAlarmQueueEmpty(), "告警队列应该为空");
        
        System.out.println("处理后告警队列大小: " + monitorAlterService.getAlarmQueueSize());
        System.out.println("✅ 处理告警队列测试通过");
    }

    @Test
    void testAlarmStatistics() {
        System.out.println("=== 测试告警统计信息 ===");
        
        // 清空队列
        monitorAlterService.clearAlarmQueue();
        
        // 获取初始统计信息
        MonitorAlterService.AlarmStatistics initialStats = monitorAlterService.getAlarmStatistics();
        long initialProcessed = initialStats.getTotalProcessed();
        
        // 添加告警并处理
        MonitorExecuteInfo executeInfo = createTestExecuteInfo("testJob", "TestMonitor", HandleCode.FAIL);
        monitorAlterService.addAlarm(executeInfo, LoggerAlterConsumer.class);
        monitorAlterService.processAlarms();
        
        // 获取处理后的统计信息
        MonitorAlterService.AlarmStatistics stats = monitorAlterService.getAlarmStatistics();
        
        // 验证统计信息
        assertTrue(stats.getTotalProcessed() > initialProcessed, "处理总数应该增加");
        assertTrue(stats.getTotalSuccess() >= 0, "成功总数应该大于等于0");
        assertTrue(stats.getTotalFailed() >= 0, "失败总数应该大于等于0");
        assertTrue(stats.getLastProcessTime() > 0, "最后处理时间应该大于0");
        
        System.out.println("统计信息: " + stats);
        System.out.println("✅ 告警统计信息测试通过");
    }

    @Test
    void testDrainAlarmQueue() {
        System.out.println("=== 测试清空告警队列 ===");
        
        // 清空队列
        monitorAlterService.clearAlarmQueue();
        
        // 添加告警
        for (int i = 1; i <= 2; i++) {
            MonitorExecuteInfo executeInfo = createTestExecuteInfo("testJob" + i, "TestMonitor" + i, HandleCode.FAIL);
            monitorAlterService.addAlarm(executeInfo, LoggerAlterConsumer.class);
        }
        
        // 验证添加后的队列大小
        assertEquals(2, monitorAlterService.getAlarmQueueSize(), "告警队列应该有2个告警");
        
        // 清空队列并获取所有告警
        java.util.List<com.innodealing.onshore.monitor.alter.AlterConsumer> drainedAlarms = monitorAlterService.drainAlarmQueue();
        
        // 验证结果
        assertEquals(2, drainedAlarms.size(), "应该清空2个告警");
        assertEquals(0, monitorAlterService.getAlarmQueueSize(), "告警队列应该为空");
        assertTrue(monitorAlterService.isAlarmQueueEmpty(), "告警队列应该为空");
        
        System.out.println("清空的告警数量: " + drainedAlarms.size());
        System.out.println("✅ 清空告警队列测试通过");
    }

    @Test
    void testNullParameterHandling() {
        System.out.println("=== 测试空参数处理 ===");
        
        // 清空队列
        monitorAlterService.clearAlarmQueue();
        int initialSize = monitorAlterService.getAlarmQueueSize();
        
        // 测试添加null告警消费者
        monitorAlterService.addAlarm((com.innodealing.onshore.monitor.alter.AlterConsumer) null);
        assertEquals(initialSize, monitorAlterService.getAlarmQueueSize(), "添加null告警消费者不应该改变队列大小");
        
        // 测试添加null执行信息
        monitorAlterService.addAlarm(null, LoggerAlterConsumer.class);
        assertEquals(initialSize, monitorAlterService.getAlarmQueueSize(), "添加null执行信息不应该改变队列大小");
        
        // 测试添加null告警消费者类型
        MonitorExecuteInfo executeInfo = createTestExecuteInfo("testJob", "TestMonitor", HandleCode.FAIL);
        monitorAlterService.addAlarm(executeInfo, null);
        assertEquals(initialSize, monitorAlterService.getAlarmQueueSize(), "添加null告警消费者类型不应该改变队列大小");
        
        System.out.println("✅ 空参数处理测试通过");
    }

    /**
     * 创建测试用的监控执行信息
     */
    private MonitorExecuteInfo createTestExecuteInfo(String jobId, String monitorName, int handleCode) {
        MonitorExecuteInfo executeInfo = new MonitorExecuteInfo(
            jobId,
            monitorName,
            "测试监控器描述",
            "测试组",
            TriggerCode.SUCCESS,
            handleCode,
            new Timestamp(System.currentTimeMillis()),
            new Timestamp(System.currentTimeMillis()),
            null
        );
        return executeInfo;
    }
}
