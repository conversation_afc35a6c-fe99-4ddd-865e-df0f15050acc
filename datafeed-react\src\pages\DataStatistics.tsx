import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, DatePicker, Button, Statistic, Space, message } from 'antd';
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface DataPoint {
  timestamp: string;
  count: number;
  volume: number;
}

interface StatisticsData {
  totalCount: number;
  totalVolume: number;
  avgRate: number;
  peakRate: number;
  dataPoints: DataPoint[];
}

const DataStatistics: React.FC = () => {
  const [timeUnit, setTimeUnit] = useState<'minute' | 'second'>('minute');
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(1, 'hour'),
    dayjs()
  ]);
  const [loading, setLoading] = useState(false);
  const [statisticsData, setStatisticsData] = useState<StatisticsData>({
    totalCount: 0,
    totalVolume: 0,
    avgRate: 0,
    peakRate: 0,
    dataPoints: []
  });

  // 模拟数据生成函数
  const generateMockData = (unit: 'minute' | 'second', startTime: dayjs.Dayjs, endTime: dayjs.Dayjs): StatisticsData => {
    const dataPoints: DataPoint[] = [];
    const duration = endTime.diff(startTime, unit);
    const interval = unit === 'minute' ? 60000 : 1000; // 毫秒

    let totalCount = 0;
    let totalVolume = 0;
    let maxRate = 0;

    for (let i = 0; i <= duration; i++) {
      const timestamp = startTime.add(i, unit).format('YYYY-MM-DD HH:mm:ss');
      const baseCount = Math.floor(Math.random() * 100) + 50;
      const count = baseCount + Math.floor(Math.sin(i / 10) * 30);
      const volume = count * (Math.random() * 1000 + 500); // 假设每条数据500-1500字节

      dataPoints.push({
        timestamp,
        count,
        volume: Math.floor(volume)
      });

      totalCount += count;
      totalVolume += volume;
      maxRate = Math.max(maxRate, count);
    }

    const avgRate = duration > 0 ? totalCount / duration : 0;

    return {
      totalCount,
      totalVolume: Math.floor(totalVolume),
      avgRate: Math.floor(avgRate),
      peakRate: maxRate,
      dataPoints
    };
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      const data = generateMockData(timeUnit, timeRange[0], timeRange[1]);
      setStatisticsData(data);

      message.success('数据加载成功');
    } catch (error) {
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchStatistics();
  }, []);

  // 格式化数据量显示
  const formatVolume = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 图表配置
  const getChartOption = () => {
    const timeFormat = timeUnit === 'minute' ? 'HH:mm' : 'HH:mm:ss';

    return {
      title: {
        text: '数据发送趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: (params: any) => {
          const data = params[0];
          const timestamp = data.axisValue;
          const count = data.value;
          const dataPoint = statisticsData.dataPoints.find(p =>
            dayjs(p.timestamp).format(timeFormat) === timestamp
          );

          return `
            <div>
              <strong>${timestamp}</strong><br/>
              发送条数: ${count}<br/>
              数据量: ${dataPoint ? formatVolume(dataPoint.volume) : 'N/A'}
            </div>
          `;
        }
      },
      legend: {
        data: ['发送条数'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: statisticsData.dataPoints.map(point =>
          dayjs(point.timestamp).format(timeFormat)
        ),
        axisLabel: {
          rotate: timeUnit === 'second' ? 45 : 0
        }
      },
      yAxis: {
        type: 'value',
        name: '条数',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '发送条数',
          type: 'line',
          smooth: true,
          data: statisticsData.dataPoints.map(point => point.count),
          areaStyle: {
            opacity: 0.3
          },
          lineStyle: {
            width: 2
          },
          symbol: 'circle',
          symbolSize: 4
        }
      ]
    };
  };

  // 导出数据
  const exportData = () => {
    const csvContent = [
      ['时间', '发送条数', '数据量(字节)'],
      ...statisticsData.dataPoints.map(point => [
        point.timestamp,
        point.count.toString(),
        point.volume.toString()
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `数据统计_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success('数据导出成功');
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 控制面板 */}
      <Card title="数据发送趋势统计" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col>
            <Space>
              <span>统计单位:</span>
              <Select
                value={timeUnit}
                onChange={setTimeUnit}
                style={{ width: 120 }}
              >
                <Option value="minute">按分钟</Option>
                <Option value="second">按秒</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <span>时间范围:</span>
              <RangePicker
                value={timeRange}
                onChange={(dates) => {
                  if (dates && dates[0] && dates[1]) {
                    setTimeRange([dates[0], dates[1]]);
                  }
                }}
                showTime={timeUnit === 'second'}
                format={timeUnit === 'second' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD HH:mm'}
                style={{ width: timeUnit === 'second' ? 380 : 300 }}
              />
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                loading={loading}
                onClick={fetchStatistics}
              >
                刷新数据
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={exportData}
                disabled={statisticsData.dataPoints.length === 0}
              >
                导出数据
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总发送条数"
              value={statisticsData.totalCount}
              precision={0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总数据量"
              value={formatVolume(statisticsData.totalVolume)}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={`平均速率(条/${timeUnit === 'minute' ? '分钟' : '秒'})`}
              value={statisticsData.avgRate}
              precision={0}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={`峰值速率(条/${timeUnit === 'minute' ? '分钟' : '秒'})`}
              value={statisticsData.peakRate}
              precision={0}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 趋势图表 */}
      <Card title="发送趋势图表" loading={loading}>
        {statisticsData.dataPoints.length > 0 ? (
          <ReactECharts
            option={getChartOption()}
            style={{ height: '400px' }}
            notMerge={true}
            lazyUpdate={true}
          />
        ) : (
          <div style={{
            height: '400px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#999'
          }}>
            暂无数据，请选择时间范围后点击刷新数据
          </div>
        )}
      </Card>
    </div>
  );
};

export default DataStatistics;