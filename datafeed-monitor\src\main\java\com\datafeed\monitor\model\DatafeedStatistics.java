package com.datafeed.monitor.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.sql.Timestamp;

/**
 * 数据发送统计模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DatafeedStatistics {
    
    /**
     * 统计ID
     */
    private Long id;
    
    /**
     * 统计时间戳
     */
    private Timestamp timestamp;
    
    /**
     * 统计单位 (minute/second)
     */
    private String timeUnit;
    
    /**
     * 发送条数
     */
    private Long sendCount;
    
    /**
     * 数据量（字节）
     */
    private Long dataVolume;
    
    /**
     * 成功条数
     */
    private Long successCount;
    
    /**
     * 失败条数
     */
    private Long failureCount;
    
    /**
     * 平均响应时间（毫秒）
     */
    private Double avgResponseTime;
    
    /**
     * 数据源
     */
    private String dataSource;
    
    /**
     * 目标系统
     */
    private String targetSystem;
    
    /**
     * 创建时间
     */
    private Timestamp createTime;
    
    /**
     * 更新时间
     */
    private Timestamp updateTime;
}
