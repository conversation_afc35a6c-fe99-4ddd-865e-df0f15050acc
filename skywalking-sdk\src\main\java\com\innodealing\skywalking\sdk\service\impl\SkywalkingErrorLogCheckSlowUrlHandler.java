package com.innodealing.skywalking.sdk.service.impl;

import com.innodealing.skywalking.sdk.module.ErrorLogDTO;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorData;
import com.innodealing.skywalking.sdk.module.SkywalkingErrorLogResponse;
import com.innodealing.skywalking.sdk.service.SkywalkingErrorLogHandler;
import com.innodealing.skywalking.sdk.service.SkywalkingHttpService;
import com.innodealing.skywalking.sdk.service.log.AbstractDingdingLogHandler;
import com.innodealing.skywalking.sdk.service.log.AbstractDingdingMarkdownLogHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * SkyWalking错误日志检查慢URL处理器
 */
public class SkywalkingErrorLogCheckSlowUrlHandler implements SkywalkingErrorLogHandler {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final SkywalkingHttpService skywalkingHttpService;

    public SkywalkingErrorLogCheckSlowUrlHandler(String skywalkingUrl) {
        this.skywalkingHttpService = new SkywalkingHttpService(skywalkingUrl);
    }

    @Override
    public Optional<ErrorLogDTO> getErrorLog(String serverName, int serverId, Set<String> endpointNameFilter, LocalDateTime start, LocalDateTime end) throws Exception {
        logger.info("CheckSlowUrlHandler getErrorLog serverId={} serverName={} start={} end={} endpointNameFilter={}", serverId, serverName, start, end, endpointNameFilter);
        SkywalkingErrorData skywalkingAllData = skywalkingHttpService.getSkywalkingAllData(serverId, start, end);
        Set<SkywalkingErrorLogResponse.Detail> details = new LinkedHashSet<>();
        List<SkywalkingErrorData.DataDTO> data = skywalkingAllData.getData();
        SkywalkingErrorLogResponse skywalkingErrorLogResponse = new SkywalkingErrorLogResponse();
        for (SkywalkingErrorData.DataDTO trace : data) {
            for (String endpointName : trace.getEndpointNames()) {
                if (endpointNameFilter.contains(endpointName)) {
                    skywalkingErrorLogResponse.addIgnoreTotal();
                    logger.warn("filter serverId:{} endpointName:{}", serverId, endpointName);
                    continue;
                }
                Integer duration = Optional.ofNullable(trace.getDuration()).orElse(0);
                if (duration >= 15 * 1000) {
                    SkywalkingErrorLogResponse.Detail detail = new SkywalkingErrorLogResponse.Detail();
                    detail.setErrorTitle("延迟");
                    detail.setErrorMessage("延迟时间毫秒：" + duration);
                    detail.setEndpointName(endpointName);
                    detail.setTraceId(trace.getTraceIds().stream().findFirst().orElse(""));
                    details.add(detail);
                }
            }
        }
        if (details.isEmpty()) {
            return Optional.empty();
        }
        skywalkingErrorLogResponse.setServerName(serverName);
        skywalkingErrorLogResponse.setTotal(data.size());
        skywalkingErrorLogResponse.setDetails(details);
        AbstractDingdingLogHandler logHandler = new AbstractDingdingMarkdownLogHandler(skywalkingErrorLogResponse);
        return logHandler.handlerLog(start, end);
    }
}
