import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Alert, Spin } from 'antd';
import { ArrowUpOutlined, UserOutlined, SendOutlined } from '@ant-design/icons';
import MockDataService from '../services/mockDataService';

const SimpleDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const dashboardData = await MockDataService.getDashboardData();
        setData(dashboardData);
        setError(null);
      } catch (err) {
        console.error('Failed to load data:', err);
        setError('数据加载失败');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (error) {
    return (
      <Alert
        message="错误"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  return (
    <Spin spinning={loading} tip="加载数据中...">
      <div>
        <div style={{ marginBottom: 24 }}>
          <h1>DataFeed 监控仪表板</h1>
          <p>最后更新: {new Date().toLocaleString()}</p>
        </div>

        {/* 关键指标 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="在线用户数"
                value={data?.onlineUserCount || 0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="今日发送量"
                value={data?.todaySendCount || 0}
                prefix={<SendOutlined />}
                suffix="条"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="失败数量"
                value={data?.failureCount || 0}
                suffix="条"
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="成功率"
                value={data?.successRate || 0}
                precision={2}
                suffix="%"
                prefix={<ArrowUpOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 在线用户列表 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="在线用户" size="small">
              {data?.onlineUsers?.length > 0 ? (
                <div>
                  {data.onlineUsers.map((user: string, index: number) => (
                    <div key={index} style={{ 
                      padding: '8px 12px', 
                      margin: '4px 0', 
                      background: '#f6ffed', 
                      border: '1px solid #b7eb8f',
                      borderRadius: '4px'
                    }}>
                      <UserOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                      {user}
                    </div>
                  ))}
                </div>
              ) : (
                <p>暂无在线用户</p>
              )}
            </Card>
          </Col>
          
          <Col xs={24} lg={12}>
            <Card title="产品统计" size="small">
              {data?.productStatistics?.length > 0 ? (
                <div>
                  {data.productStatistics.map((product: any, index: number) => (
                    <div key={index} style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      padding: '8px 0',
                      borderBottom: index < data.productStatistics.length - 1 ? '1px solid #f0f0f0' : 'none'
                    }}>
                      <span>{product.productCode}</span>
                      <span>
                        <strong>{product.count}</strong> 
                        <small style={{ color: '#666', marginLeft: 8 }}>
                          ({product.percentage}%)
                        </small>
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p>暂无产品数据</p>
              )}
            </Card>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default SimpleDashboard;
