package com.innodealing.skywalking.sdk.config;

import com.innodealing.skywalking.sdk.service.SkywalkingErrorLogHandler;
import com.innodealing.skywalking.sdk.service.SkywalkingHttpService;
import com.innodealing.skywalking.sdk.service.SkywalkingService;
import com.innodealing.skywalking.sdk.service.impl.SkywalkingErrorLogCheckErrorHandler;
import com.innodealing.skywalking.sdk.service.impl.SkywalkingErrorLogCheckSlowUrlHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * SkyWalking SDK自动配置
 */
@Configuration
@EnableConfigurationProperties(SkywalkingProperties.class)
@ConditionalOnProperty(prefix = "skywalking", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SkywalkingAutoConfiguration {

    /**
     * SkyWalking HTTP服务
     */
    @Bean
    @ConditionalOnMissingBean
    public SkywalkingHttpService skywalkingHttpService(SkywalkingProperties properties) {
        return new SkywalkingHttpService(properties.getUrl());
    }

    /**
     * SkyWalking错误日志处理器列表
     */
    @Bean
    @ConditionalOnMissingBean
    public List<SkywalkingErrorLogHandler> skywalkingErrorLogHandlers(SkywalkingProperties properties) {
        List<SkywalkingErrorLogHandler> handlers = new ArrayList<>();
        handlers.add(new SkywalkingErrorLogCheckErrorHandler(properties.getUrl()));
        handlers.add(new SkywalkingErrorLogCheckSlowUrlHandler(properties.getUrl()));
        return handlers;
    }

    /**
     * SkyWalking服务
     */
    @Bean
    @ConditionalOnMissingBean
    public SkywalkingService skywalkingService(SkywalkingProperties properties, 
                                              List<SkywalkingErrorLogHandler> handlers) {
        return new SkywalkingService(properties, handlers);
    }
}
