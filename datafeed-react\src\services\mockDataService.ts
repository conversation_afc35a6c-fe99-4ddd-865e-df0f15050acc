// Mock数据服务 - 用于前端独立运行
export interface TrendDataPoint {
  timePoint: string;
  count: number;
}

export interface ProductStatistics {
  productCode: string;
  count: number;
  percentage: number;
}

export interface DashboardData {
  onlineUserCount: number;
  onlineUsers: string[];
  todaySendCount: number;
  failureCount: number;
  successRate: number;
  trendData: TrendDataPoint[];
  productStatistics: ProductStatistics[];
}

export interface OnlineUsersResponse {
  count: number;
  users: string[];
}

export interface TodayStatistics {
  sendCount: number;
  failureCount: number;
  successCount: number;
  successRate: number;
}

// Mock数据生成器
class MockDataService {
  private static generateRandomUsers(): string[] {
    const users = [
      'user001', 'user002', 'user003', 'user004', 'user005',
      'user006', 'user007', 'user008', 'user009', 'user010',
      'admin001', 'admin002', 'test001', 'test002', 'demo001'
    ];
    const count = Math.floor(Math.random() * 8) + 3; // 3-10个用户
    return users.slice(0, count);
  }

  private static generateTrendData(): TrendDataPoint[] {
    const data: TrendDataPoint[] = [];
    const now = new Date();
    
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000); // 每小时一个点
      const baseCount = 100 + Math.floor(Math.random() * 200); // 100-300基础值
      const wave = Math.sin(i * Math.PI / 12) * 50; // 波动
      const count = Math.max(10, Math.floor(baseCount + wave + (Math.random() - 0.5) * 40));
      
      data.push({
        timePoint: time.toISOString(),
        count: count
      });
    }
    
    return data;
  }

  private static generateProductStats(): ProductStatistics[] {
    const products = ['BOND', 'EQUITY', 'FOREX', 'COMMODITY', 'CRYPTO'];
    const stats: ProductStatistics[] = [];
    let totalCount = 0;
    const counts: number[] = [];

    // 生成随机数量
    products.forEach(() => {
      const count = Math.floor(Math.random() * 5000) + 1000; // 1000-6000
      counts.push(count);
      totalCount += count;
    });

    // 计算百分比
    products.forEach((product, index) => {
      const count = counts[index];
      const percentage = Math.round((count / totalCount) * 100 * 100) / 100; // 保留2位小数
      
      stats.push({
        productCode: product,
        count: count,
        percentage: percentage
      });
    });

    return stats;
  }

  // 模拟API延迟
  private static delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 获取仪表板数据
  static async getDashboardData(): Promise<DashboardData> {
    await this.delay();
    
    const onlineUsers = this.generateRandomUsers();
    const trendData = this.generateTrendData();
    const productStats = this.generateProductStats();
    const todaySendCount = trendData.reduce((sum, item) => sum + item.count, 0);
    const failureCount = Math.floor(Math.random() * 100) + 20;
    const successRate = Math.round((1 - failureCount / todaySendCount) * 100 * 100) / 100;

    return {
      onlineUserCount: onlineUsers.length,
      onlineUsers: onlineUsers,
      todaySendCount: todaySendCount,
      failureCount: failureCount,
      successRate: Math.max(95, successRate), // 确保成功率不低于95%
      trendData: trendData,
      productStatistics: productStats
    };
  }

  // 获取在线用户
  static async getOnlineUsers(): Promise<OnlineUsersResponse> {
    await this.delay();
    
    const users = this.generateRandomUsers();
    return {
      count: users.length,
      users: users
    };
  }

  // 获取今日统计
  static async getTodayStatistics(): Promise<TodayStatistics> {
    await this.delay();
    
    const sendCount = Math.floor(Math.random() * 50000) + 10000; // 10000-60000
    const failureCount = Math.floor(Math.random() * 500) + 50; // 50-550
    const successCount = sendCount - failureCount;
    const successRate = Math.round((successCount / sendCount) * 100 * 100) / 100;

    return {
      sendCount: sendCount,
      failureCount: failureCount,
      successCount: successCount,
      successRate: successRate
    };
  }

  // 获取实时趋势数据
  static async getRealtimeTrend(timeUnit: 'minute' | 'second' = 'minute', minutes: number = 60): Promise<TrendDataPoint[]> {
    await this.delay();
    
    const data: TrendDataPoint[] = [];
    const now = new Date();
    const interval = timeUnit === 'minute' ? 60000 : 1000; // 毫秒
    const points = timeUnit === 'minute' ? Math.min(minutes, 60) : Math.min(minutes * 60, 300); // 限制数据点数量

    for (let i = points - 1; i >= 0; i--) {
      const time = new Date(now.getTime() - i * interval);
      const baseCount = 50 + Math.floor(Math.random() * 100); // 50-150基础值
      const wave = Math.sin(i * Math.PI / 20) * 20; // 波动
      const count = Math.max(10, Math.floor(baseCount + wave + (Math.random() - 0.5) * 20));
      
      data.push({
        timePoint: time.toISOString(),
        count: count
      });
    }
    
    return data;
  }

  // 获取产品统计
  static async getProductStatistics(): Promise<ProductStatistics[]> {
    await this.delay();
    return this.generateProductStats();
  }

  // 健康检查
  static async healthCheck(): Promise<{ status: string; timestamp: string }> {
    await this.delay(200);
    return {
      status: 'OK',
      timestamp: new Date().toISOString()
    };
  }
}

export default MockDataService;
