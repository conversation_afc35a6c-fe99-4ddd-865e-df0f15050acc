package com.datafeed.monitor.model;

import java.util.List;

/**
 * 在线用户响应
 */
public class OnlineUsersResponse {
    
    /**
     * 在线用户数量
     */
    private Integer count;
    
    /**
     * 在线用户列表
     */
    private List<String> users;
    
    public OnlineUsersResponse() {}
    
    public OnlineUsersResponse(Integer count, List<String> users) {
        this.count = count;
        this.users = users;
    }
    
    public Integer getCount() {
        return count;
    }
    
    public void setCount(Integer count) {
        this.count = count;
    }
    
    public List<String> getUsers() {
        return users;
    }
    
    public void setUsers(List<String> users) {
        this.users = users;
    }
    
    @Override
    public String toString() {
        return "OnlineUsersResponse{" +
                "count=" + count +
                ", users=" + users +
                '}';
    }
}
