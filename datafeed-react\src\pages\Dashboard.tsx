import React, { useMemo } from 'react';
import { Row, Col, Card, Statistic, Table, Tag, Progress, Alert, Spin, Button } from 'antd';
import { ArrowUpOutlined, UserOutlined, SendOutlined, ReloadOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useDatafeedData } from '../hooks/useDatafeedData';

const Dashboard: React.FC = () => {
  // 使用自定义Hook获取数据
  const {
    dashboardData,
    trendData,
    productStats,
    onlineUsers,
    todayStats,
    loading,
    error,
    refreshData,
    refreshTrendData
  } = useDatafeedData(true, 30000); // 自动刷新，30秒间隔

  // 计算统计数据
  const accountStats = useMemo(() => {
    if (!onlineUsers) return { total: 0, online: 0, offline: 0, onlineRate: 0 };

    const online = onlineUsers.count;
    const total = online + 50; // 假设总数比在线数多一些，实际应该从API获取
    const offline = total - online;
    const onlineRate = total > 0 ? Math.round((online / total) * 100) : 0;

    return { total, online, offline, onlineRate };
  }, [onlineUsers]);

  const sendStats = useMemo(() => {
    if (!todayStats) return { totalSent: 0, successRate: 0, errorCount: 0, todayIncrease: 0 };

    return {
      totalSent: todayStats.sendCount,
      successRate: todayStats.successRate,
      errorCount: todayStats.failureCount,
      todayIncrease: 12.5 // 这个需要计算昨天的增长率，暂时使用固定值
    };
  }, [todayStats]);

  // 在线账号数据
  const onlineAccountsData = useMemo(() => {
    if (!onlineUsers?.users) return [];

    return onlineUsers.users.slice(0, 10).map((user, index) => ({
      key: index.toString(),
      account: user,
      status: 'online',
      lastLogin: new Date(Date.now() - Math.random() * 3600000).toLocaleString(),
      lastHeartbeat: new Date(Date.now() - Math.random() * 300000).toLocaleString(),
      onlineTime: `${Math.floor(Math.random() * 8)}小时${Math.floor(Math.random() * 60)}分钟`,
      template: `Template_${String.fromCharCode(65 + Math.floor(Math.random() * 5))}`
    }));
  }, [onlineUsers]);

  const accountColumns = [
    {
      title: '账号',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '离线'}
        </Tag>
      ),
    },
    {
      title: '上次登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
    },
    {
      title: '上次心跳',
      dataIndex: 'lastHeartbeat',
      key: 'lastHeartbeat',
    },
    {
      title: '在线时长',
      dataIndex: 'onlineTime',
      key: 'onlineTime',
    },
    {
      title: '模板',
      dataIndex: 'template',
      key: 'template',
    },
  ];

  // 数据发送趋势图表配置
  const sendTrendOption = useMemo(() => {
    const timePoints = trendData.map(item => {
      const date = new Date(item.timePoint);
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    });
    const counts = trendData.map(item => item.count);

    return {
      title: {
        text: '数据发送趋势'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}<br/>发送量: ${data.value}`;
        }
      },
      xAxis: {
        type: 'category',
        data: timePoints.length > 0 ? timePoints : ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: counts.length > 0 ? counts : [820, 932, 901, 934, 1290, 1330, 1320],
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#1890ff'
          }
        }
      ]
    };
  }, [trendData]);

  // 产品发送量分布饼图配置
  const productDistributionOption = useMemo(() => {
    const pieData = productStats.length > 0
      ? productStats.map(item => ({
          value: item.count,
          name: item.productCode
        }))
      : [
          { value: 1048, name: 'BOND' },
          { value: 735, name: 'EQUITY' },
          { value: 580, name: 'FOREX' },
          { value: 484, name: 'COMMODITY' },
          { value: 300, name: 'CRYPTO' }
        ];

    return {
      title: {
        text: '产品发送量分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '发送量',
          type: 'pie',
          radius: '50%',
          data: pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  }, [productStats]);

  // 最新告警
  const recentAlerts = [
    {
      id: 1,
      type: 'error',
      message: 'account_025 连接异常，已离线超过30分钟',
      time: '2024-01-15 14:20:15'
    },
    {
      id: 2,
      type: 'warning',
      message: 'Template_C 发送失败率超过5%',
      time: '2024-01-15 14:15:30'
    },
    {
      id: 3,
      type: 'info',
      message: '系统正常运行，无异常',
      time: '2024-01-15 14:10:00'
    }
  ];

  // 错误处理
  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={refreshData}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <Spin spinning={loading} tip="加载数据中...">
      <div>
        <div className="dashboard-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <h1 className="dashboard-title">DataFeed 监控总览</h1>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <span>最后更新: {new Date().toLocaleString()}</span>
            <Button
              icon={<ReloadOutlined />}
              onClick={refreshData}
              loading={loading}
            >
              刷新
            </Button>
          </div>
        </div>

      {/* 关键指标统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总账号数"
              value={accountStats.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="在线账号"
              value={accountStats.online}
              prefix={<UserOutlined />}
              suffix={`/ ${accountStats.total}`}
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress 
              percent={accountStats.onlineRate} 
              size="small" 
              status="active"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日发送量"
              value={sendStats.totalSent}
              prefix={<SendOutlined />}
              suffix={
                <span style={{ fontSize: 12, color: '#52c41a' }}>
                  <ArrowUpOutlined /> {sendStats.todayIncrease}%
                </span>
              }
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="发送成功率"
              value={sendStats.successRate}
              prefix={<SendOutlined />}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="数据发送趋势">
            <ReactECharts option={sendTrendOption} style={{ height: 300 }} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="产品发送量分布">
            <ReactECharts option={productDistributionOption} style={{ height: 300 }} />
          </Card>
        </Col>
      </Row>

      {/* 账号状态和告警 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="在线账号状态" extra={<a href="/account-monitor">查看全部</a>}>
            <Table 
              columns={accountColumns} 
              dataSource={onlineAccountsData}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="最新告警" extra={<a href="/alert-center">查看全部</a>}>
            {recentAlerts.map(alert => (
              <Alert
                key={alert.id}
                message={alert.message}
                description={alert.time}
                type={alert.type as any}
                showIcon
                style={{ marginBottom: 8 }}
              />
            ))}
          </Card>
        </Col>
      </Row>
      </div>
    </Spin>
  );
};

export default Dashboard; 