import React from 'react';
import { Row, Col, Card, Statistic, Table, Tag, Progress, Alert } from 'antd';
import { ArrowUpOutlined, UserOutlined, SendOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const Dashboard: React.FC = () => {
  // 模拟数据
  const accountStats = {
    total: 248,
    online: 186,
    offline: 62,
    onlineRate: 75
  };

  const sendStats = {
    totalSent: 125680,
    successRate: 98.5,
    errorCount: 1892,
    todayIncrease: 12.5
  };

  // 在线账号数据
  const onlineAccountsData = [
    {
      key: '1',
      account: 'account_001',
      status: 'online',
      lastLogin: '2024-01-15 09:30:15',
      lastHeartbeat: '2024-01-15 14:25:32',
      onlineTime: '4小时55分钟',
      template: 'Template_A'
    },
    {
      key: '2',
      account: 'account_002',
      status: 'online',
      lastLogin: '2024-01-15 08:15:20',
      lastHeartbeat: '2024-01-15 14:24:18',
      onlineTime: '6小时10分钟',
      template: 'Template_B'
    },
    {
      key: '3',
      account: 'account_003',
      status: 'offline',
      lastLogin: '2024-01-15 07:45:10',
      lastHeartbeat: '2024-01-15 13:20:45',
      onlineTime: '0分钟',
      template: 'Template_A'
    }
  ];

  const accountColumns = [
    {
      title: '账号',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '离线'}
        </Tag>
      ),
    },
    {
      title: '上次登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
    },
    {
      title: '上次心跳',
      dataIndex: 'lastHeartbeat',
      key: 'lastHeartbeat',
    },
    {
      title: '在线时长',
      dataIndex: 'onlineTime',
      key: 'onlineTime',
    },
    {
      title: '模板',
      dataIndex: 'template',
      key: 'template',
    },
  ];

  // 数据发送趋势图表配置
  const sendTrendOption = {
    title: {
      text: '数据发送趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        type: 'line',
        smooth: true
      }
    ]
  };

  // 模板发送量分布饼图配置
  const templateDistributionOption = {
    title: {
      text: '模板发送量分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '发送量',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: 'Template_A' },
          { value: 735, name: 'Template_B' },
          { value: 580, name: 'Template_C' },
          { value: 484, name: 'Template_D' },
          { value: 300, name: 'Template_E' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  // 最新告警
  const recentAlerts = [
    {
      id: 1,
      type: 'error',
      message: 'account_025 连接异常，已离线超过30分钟',
      time: '2024-01-15 14:20:15'
    },
    {
      id: 2,
      type: 'warning',
      message: 'Template_C 发送失败率超过5%',
      time: '2024-01-15 14:15:30'
    },
    {
      id: 3,
      type: 'info',
      message: '系统正常运行，无异常',
      time: '2024-01-15 14:10:00'
    }
  ];

  return (
    <div>
      <div className="dashboard-header">
        <h1 className="dashboard-title">数据源监控总览</h1>
        <div>
          <span>最后更新: 2024-01-15 14:25:00</span>
        </div>
      </div>

      {/* 关键指标统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总账号数"
              value={accountStats.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="在线账号"
              value={accountStats.online}
              prefix={<UserOutlined />}
              suffix={`/ ${accountStats.total}`}
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress 
              percent={accountStats.onlineRate} 
              size="small" 
              status="active"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日发送量"
              value={sendStats.totalSent}
              prefix={<SendOutlined />}
              suffix={
                <span style={{ fontSize: 12, color: '#52c41a' }}>
                  <ArrowUpOutlined /> {sendStats.todayIncrease}%
                </span>
              }
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="发送成功率"
              value={sendStats.successRate}
              prefix={<SendOutlined />}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="数据发送趋势">
            <ReactECharts option={sendTrendOption} style={{ height: 300 }} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="模板发送量分布">
            <ReactECharts option={templateDistributionOption} style={{ height: 300 }} />
          </Card>
        </Col>
      </Row>

      {/* 账号状态和告警 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="在线账号状态" extra={<a href="/account-monitor">查看全部</a>}>
            <Table 
              columns={accountColumns} 
              dataSource={onlineAccountsData}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="最新告警" extra={<a href="/alert-center">查看全部</a>}>
            {recentAlerts.map(alert => (
              <Alert
                key={alert.id}
                message={alert.message}
                description={alert.time}
                type={alert.type as any}
                showIcon
                style={{ marginBottom: 8 }}
              />
            ))}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard; 