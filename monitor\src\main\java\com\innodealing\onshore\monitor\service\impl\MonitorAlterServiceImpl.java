package com.innodealing.onshore.monitor.service.impl;

import com.innodealing.onshore.monitor.alter.AlterConsumer;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.service.MonitorAlterService;
import com.innodealing.onshore.monitor.utils.AlterConsumerUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 监控告警服务实现类
 * 负责管理监控告警的队列、处理和发送
 */
@Service
public class MonitorAlterServiceImpl implements MonitorAlterService {

    private static final Logger logger = LoggerFactory.getLogger(MonitorAlterServiceImpl.class);

    /**
     * 告警队列，使用线程安全的双端队列
     */
    private final Deque<AlterConsumer> alarmQueue = new LinkedBlockingDeque<>();

    /**
     * 统计信息
     */
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalSuccess = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    private volatile long lastProcessTime = 0;

    @Override
    public void addAlarm(AlterConsumer alterConsumer) {
        if (alterConsumer == null) {
            logger.warn("Attempted to add null AlterConsumer to alarm queue");
            return;
        }
        
        alarmQueue.push(alterConsumer);
        logger.debug("Added alarm to queue, current queue size: {}", alarmQueue.size());
    }

    @Override
    public void addAlarm(MonitorExecuteInfo executeInfo, Class<? extends AlterConsumer> alterConsumerClass) {
        if (executeInfo == null) {
            logger.warn("Attempted to add alarm with null MonitorExecuteInfo");
            return;
        }
        
        if (alterConsumerClass == null) {
            logger.warn("Attempted to add alarm with null AlterConsumer class for jobId: {}", executeInfo.getJobId());
            return;
        }
        
        try {
            AlterConsumer alterConsumer = AlterConsumerUtils.getConsumer(executeInfo, alterConsumerClass);
            addAlarm(alterConsumer);
            logger.debug("Created and added alarm for jobId: {}, alterConsumer: {}", 
                executeInfo.getJobId(), alterConsumerClass.getSimpleName());
        } catch (Exception e) {
            logger.error("Failed to create AlterConsumer for jobId: {}, alterConsumer: {}", 
                executeInfo.getJobId(), alterConsumerClass.getSimpleName(), e);
        }
    }

    @Override
    @Scheduled(cron = "*/5 * * * * ?")
    public void processAlarms() {
        if (alarmQueue.isEmpty()) {
            return;
        }
        
        int processedCount = 0;
        int successCount = 0;
        int failedCount = 0;
        
        logger.debug("Starting alarm processing, queue size: {}", alarmQueue.size());
        
        while (!alarmQueue.isEmpty()) {
            AlterConsumer alterConsumer = alarmQueue.poll();
            if (alterConsumer == null) {
                break;
            }
            
            processedCount++;
            
            try {
                alterConsumer.alter();
                successCount++;
                logger.debug("Successfully processed alarm: {}", alterConsumer.getClass().getSimpleName());
            } catch (Exception e) {
                failedCount++;
                logger.error("Failed to process alarm: {}", alterConsumer.getClass().getSimpleName(), e);
            }
        }
        
        // 更新统计信息
        totalProcessed.addAndGet(processedCount);
        totalSuccess.addAndGet(successCount);
        totalFailed.addAndGet(failedCount);
        lastProcessTime = System.currentTimeMillis();
        
        if (processedCount > 0) {
            logger.info("Alarm processing completed: processed={}, success={}, failed={}", 
                processedCount, successCount, failedCount);
        }
    }

    @Override
    public int getAlarmQueueSize() {
        return alarmQueue.size();
    }

    @Override
    public List<AlterConsumer> drainAlarmQueue() {
        List<AlterConsumer> drained = new ArrayList<>();
        AlterConsumer consumer;
        while ((consumer = alarmQueue.poll()) != null) {
            drained.add(consumer);
        }
        logger.info("Drained {} alarms from queue", drained.size());
        return drained;
    }

    @Override
    public void clearAlarmQueue() {
        int clearedCount = alarmQueue.size();
        alarmQueue.clear();
        logger.info("Cleared {} alarms from queue", clearedCount);
    }

    @Override
    public boolean isAlarmQueueEmpty() {
        return alarmQueue.isEmpty();
    }

    @Override
    public AlarmStatistics getAlarmStatistics() {
        return new AlarmStatistics(
            totalProcessed.get(),
            totalSuccess.get(),
            totalFailed.get(),
            lastProcessTime
        );
    }
}
