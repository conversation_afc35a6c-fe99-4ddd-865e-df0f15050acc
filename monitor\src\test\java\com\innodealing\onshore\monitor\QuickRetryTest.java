package com.innodealing.onshore.monitor;

import com.innodealing.onshore.monitor.demo.RetryDemoMonitor;
import com.innodealing.onshore.monitor.demo.FailDemoMonitor;
import com.innodealing.onshore.monitor.model.HandleCode;
import com.innodealing.onshore.monitor.model.MonitorExecuteInfo;
import com.innodealing.onshore.monitor.service.MonitorService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 快速重试功能验证测试
 */
@SpringBootTest
@ActiveProfiles("local")
class QuickRetryTest {

    @Resource
    private MonitorService monitorService;



    @Test
    void testSyncRetrySuccess() {

        RetryDemoMonitor.resetCallCount();

        System.out.println("=== 测试同步执行重试成功场景 ===");

        // 同步执行监控，前两次会失败，第三次成功
        MonitorExecuteInfo result = monitorService.execute("RetryDemoMonitor");

        // 验证结果
        assertNotNull(result, "执行结果不应为空");
        assertEquals(HandleCode.SUCCESS, result.getHandleCode().intValue(), "最终状态应该是成功");
        assertEquals(2, result.getRetryCount().intValue(), "应该重试了2次");
        assertEquals(2, result.getMaxRetryCount().intValue(), "最大重试次数应该是2");

        // 验证重试历史
        assertNotNull(result.getRetryHistory(), "重试历史不应为空");
        assertEquals(2, result.getRetryHistory().size(), "应该有2条重试记录");

        // 验证总调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, RetryDemoMonitor.getCallCount(), "总调用次数应该是3次");

        // 验证最终成功
        assertNotNull(result.getMonitorReport(), "监控报告不应为空");
        assertTrue(result.getMonitorReport().isAccess(), "监控应该最终成功");

        System.out.println("✅ 同步重试成功测试通过");
        System.out.println("最终状态: " + result.getHandleCode());
        System.out.println("重试次数: " + result.getRetryCount());
        System.out.println("总调用次数: " + RetryDemoMonitor.getCallCount());
    }

    @Test
    void testSyncRetryFailure() {
        FailDemoMonitor.resetCallCount();

        System.out.println("=== 测试同步执行重试失败场景 ===");

        // 同步执行监控，始终失败
        MonitorExecuteInfo result = monitorService.execute("FailDemoMonitor");

        // 验证结果
        assertNotNull(result, "执行结果不应为空");
        assertEquals(HandleCode.EXCEPTION, result.getHandleCode().intValue(), "最终状态应该是异常");
        assertEquals(2, result.getRetryCount().intValue(), "应该重试了2次");
        assertEquals(2, result.getMaxRetryCount().intValue(), "最大重试次数应该是2");

        // 验证重试历史
        assertNotNull(result.getRetryHistory(), "重试历史不应为空");
        assertEquals(2, result.getRetryHistory().size(), "应该有2条重试记录");

        // 验证总调用次数：初始1次 + 重试2次 = 3次
        assertEquals(3, FailDemoMonitor.getCallCount(), "总调用次数应该是3次");

        System.out.println("✅ 同步重试失败测试通过");
        System.out.println("最终状态: " + result.getHandleCode());
        System.out.println("重试次数: " + result.getRetryCount());
        System.out.println("总调用次数: " + FailDemoMonitor.getCallCount());
    }

    @Test
    void testAsyncRetryInitialState() {
        RetryDemoMonitor.resetCallCount();

        System.out.println("=== 测试异步执行初始状态 ===");

        // 异步执行监控
        MonitorExecuteInfo result = monitorService.execute("RetryDemoMonitor");

        // 验证初始状态
        assertNotNull(result, "执行结果不应为空");
        assertEquals(HandleCode.RUNNING, result.getHandleCode().intValue(), "异步执行初始状态应该是RUNNING");
        assertEquals(0, result.getRetryCount().intValue(), "初始重试次数应该是0");
        assertEquals(2, result.getMaxRetryCount().intValue(), "最大重试次数应该是2");

        System.out.println("✅ 异步执行初始状态测试通过");
        System.out.println("初始状态: " + result.getHandleCode());
        System.out.println("任务ID: " + result.getJobId());

        // 等待一小段时间，然后检查任务是否开始执行
        try {
            Thread.sleep(2000); // 等待2秒
            int callCount = RetryDemoMonitor.getCallCount();
            System.out.println("2秒后调用次数: " + callCount);
            assertTrue(callCount >= 1, "异步任务应该已经开始执行");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    void testRetryConfiguration() {
        System.out.println("=== 测试重试配置 ===");
        
        // 创建一个新的执行信息来验证默认配置
        MonitorExecuteInfo executeInfo = new MonitorExecuteInfo();
        assertEquals(0, executeInfo.getRetryCount().intValue(), "初始重试次数应该是0");
        assertEquals(2, executeInfo.getMaxRetryCount().intValue(), "默认最大重试次数应该是2");
        assertNotNull(executeInfo.getRetryHistory(), "重试历史应该被初始化");
        assertTrue(executeInfo.getRetryHistory().isEmpty(), "初始重试历史应该为空");
        
        System.out.println("✅ 重试配置测试通过");
    }

    @Test
    void testRetryRecord() {
        System.out.println("=== 测试重试记录功能 ===");

        RetryDemoMonitor.resetCallCount();

        // 同步执行监控，验证重试记录
        MonitorExecuteInfo result = monitorService.execute("RetryDemoMonitor");
        
        // 验证重试记录详情
        assertEquals(2, result.getRetryHistory().size(), "应该有2条重试记录");
        
        for (int i = 0; i < result.getRetryHistory().size(); i++) {
            MonitorExecuteInfo.RetryRecord record = result.getRetryHistory().get(i);
            assertEquals(i + 1, record.getRetryNumber().intValue(), "重试序号应该正确");
            assertNotNull(record.getRetryTime(), "重试时间不应为空");
            assertEquals("Task failed, attempting retry", record.getReason(), "重试原因应该正确");
            assertNotNull(record.getResultCode(), "重试结果代码不应为空");
            
            System.out.println(String.format("重试记录 %d: 时间=%s, 原因=%s, 结果=%s", 
                record.getRetryNumber(), 
                record.getRetryTime(), 
                record.getReason(),
                record.getResultCode()
            ));
        }
        
        System.out.println("✅ 重试记录功能测试通过");
    }
}
